'use client';

import React, { useState, useEffect } from 'react';
import { Eye, TrendingUp, DollarSign, BarChart3, Plus, Edit, Trash2, Settings, X, Save, Upload } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';
import { filterByDateRange } from '@/utils/dateFilters';

/**
 * 广告管理页面 - 专门管理广告投放、收益分析和广告位管理
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const AdvertisingPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAd, setEditingAd] = useState<any>(null);
  const [editForm, setEditForm] = useState({
    name: '',
    type: '',
    status: '',
    revenue: '',
    impressions: '',
    ctr: '',
    clicks: ''
  });
  const [selectedAds, setSelectedAds] = useState<string[]>([]);
  const [adFilter, setAdFilter] = useState('all');

  // 广告统计数据
  const [adStats, setAdStats] = useState({
    totalRevenue: 45600,
    totalImpressions: 245000,
    averageCTR: 3.2,
    activeAds: 12,
    totalClicks: 7840,
    averageCPM: 2.85
  });

  // 广告收入趋势数据
  const [adRevenueData, setAdRevenueData] = useState([
    { name: 'Mon', revenue: 1200, impressions: 24000, ctr: 2.5, clicks: 600 },
    { name: 'Tue', revenue: 1800, impressions: 32000, ctr: 3.1, clicks: 992 },
    { name: 'Wed', revenue: 1500, impressions: 28000, ctr: 2.8, clicks: 784 },
    { name: 'Thu', revenue: 2100, impressions: 35000, ctr: 3.5, clicks: 1225 },
    { name: 'Fri', revenue: 2400, impressions: 42000, ctr: 3.8, clicks: 1596 },
    { name: 'Sat', revenue: 1900, impressions: 31000, ctr: 3.2, clicks: 992 },
    { name: 'Sun', revenue: 1600, impressions: 26000, ctr: 2.9, clicks: 754 }
  ]);

  // 广告位数据
  const [adPlacements, setAdPlacements] = useState([
    { id: '1', name: 'Header Banner', type: 'Banner Ad', status: 'Active', revenue: 2500, impressions: 45000, ctr: 3.2, clicks: 1440 },
    { id: '2', name: 'Sidebar Ad', type: 'Display Ad', status: 'Active', revenue: 1500, impressions: 30000, ctr: 2.5, clicks: 750 },
    { id: '3', name: 'Content Ad', type: 'Native Ad', status: 'Active', revenue: 1800, impressions: 25000, ctr: 4.1, clicks: 1025 },
    { id: '4', name: 'Footer Banner', type: 'Banner Ad', status: 'Paused', revenue: 800, impressions: 12000, ctr: 2.8, clicks: 336 },
    { id: '5', name: 'Mobile Banner', type: 'Mobile Ad', status: 'Active', revenue: 2200, impressions: 38000, ctr: 3.6, clicks: 1368 },
    { id: '6', name: 'Video Ad', type: 'Video Ad', status: 'Active', revenue: 3200, impressions: 15000, ctr: 5.2, clicks: 780 }
  ]);

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    setAdStats({
      totalRevenue: Math.floor(45600 * multiplier),
      totalImpressions: Math.floor(245000 * multiplier),
      averageCTR: 3.2 + (Math.random() - 0.5) * 0.5, // 小幅波动
      activeAds: Math.floor(12 * Math.max(0.5, multiplier)),
      totalClicks: Math.floor(7840 * multiplier),
      averageCPM: 2.85 + (Math.random() - 0.5) * 0.3
    });

    // 更新图表数据 - 根据天数范围决定数据粒度
    if (daysDiff <= 7) {
      // 7天内显示每日数据
      setAdRevenueData([
        { name: 'Mon', revenue: Math.floor(1200 * multiplier), impressions: Math.floor(24000 * multiplier), ctr: 2.5, clicks: Math.floor(600 * multiplier) },
        { name: 'Tue', revenue: Math.floor(1800 * multiplier), impressions: Math.floor(32000 * multiplier), ctr: 3.1, clicks: Math.floor(992 * multiplier) },
        { name: 'Wed', revenue: Math.floor(1500 * multiplier), impressions: Math.floor(28000 * multiplier), ctr: 2.8, clicks: Math.floor(784 * multiplier) },
        { name: 'Thu', revenue: Math.floor(2100 * multiplier), impressions: Math.floor(35000 * multiplier), ctr: 3.5, clicks: Math.floor(1225 * multiplier) },
        { name: 'Fri', revenue: Math.floor(2400 * multiplier), impressions: Math.floor(42000 * multiplier), ctr: 3.8, clicks: Math.floor(1596 * multiplier) },
        { name: 'Sat', revenue: Math.floor(1900 * multiplier), impressions: Math.floor(31000 * multiplier), ctr: 3.2, clicks: Math.floor(992 * multiplier) },
        { name: 'Sun', revenue: Math.floor(1600 * multiplier), impressions: Math.floor(26000 * multiplier), ctr: 2.9, clicks: Math.floor(754 * multiplier) }
      ]);
    } else if (daysDiff <= 30) {
      // 30天内显示每周数据
      setAdRevenueData([
        { name: 'Week 1', revenue: Math.floor(8500 * multiplier), impressions: Math.floor(180000 * multiplier), ctr: 3.2, clicks: Math.floor(5760 * multiplier) },
        { name: 'Week 2', revenue: Math.floor(9200 * multiplier), impressions: Math.floor(195000 * multiplier), ctr: 3.5, clicks: Math.floor(6825 * multiplier) },
        { name: 'Week 3', revenue: Math.floor(7800 * multiplier), impressions: Math.floor(165000 * multiplier), ctr: 3.1, clicks: Math.floor(5115 * multiplier) },
        { name: 'Week 4', revenue: Math.floor(10100 * multiplier), impressions: Math.floor(210000 * multiplier), ctr: 3.8, clicks: Math.floor(7980 * multiplier) }
      ]);
    } else {
      // 超过30天显示每月数据
      setAdRevenueData([
        { name: 'Month 1', revenue: Math.floor(32000 * multiplier), impressions: Math.floor(650000 * multiplier), ctr: 3.1, clicks: Math.floor(20150 * multiplier) },
        { name: 'Month 2', revenue: Math.floor(38000 * multiplier), impressions: Math.floor(780000 * multiplier), ctr: 3.4, clicks: Math.floor(26520 * multiplier) },
        { name: 'Month 3', revenue: Math.floor(42000 * multiplier), impressions: Math.floor(850000 * multiplier), ctr: 3.6, clicks: Math.floor(30600 * multiplier) }
      ]);
    }
  };

  // 处理广告位操作
  const handleEditAd = (adId: string) => {
    const adToEdit = adPlacements.find(ad => ad.id === adId);
    if (adToEdit) {
      setEditingAd(adToEdit);
      setEditForm({
        name: adToEdit.name,
        type: adToEdit.type,
        status: adToEdit.status,
        revenue: adToEdit.revenue.toString(),
        impressions: adToEdit.impressions.toString(),
        ctr: adToEdit.ctr.toString(),
        clicks: adToEdit.clicks.toString()
      });
      setShowEditModal(true);
    }
  };

  const handleSaveEdit = () => {
    if (editingAd) {
      setAdPlacements(prev => prev.map(ad =>
        ad.id === editingAd.id
          ? {
              ...ad,
              name: editForm.name,
              type: editForm.type,
              status: editForm.status,
              revenue: parseFloat(editForm.revenue) || 0,
              impressions: parseInt(editForm.impressions) || 0,
              ctr: parseFloat(editForm.ctr) || 0,
              clicks: parseInt(editForm.clicks) || 0
            }
          : ad
      ));
      setShowEditModal(false);
      setEditingAd(null);
      alert('Ad placement updated successfully!');
    }
  };

  const handleDeleteAd = (adId: string) => {
    if (confirm('Are you sure you want to delete this ad placement?')) {
      setAdPlacements(prev => prev.filter(ad => ad.id !== adId));
    }
  };

  const handleToggleAdStatus = (adId: string) => {
    setAdPlacements(prev => prev.map(ad => 
      ad.id === adId 
        ? { ...ad, status: ad.status === 'Active' ? 'Paused' : 'Active' }
        : ad
    ));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Advertising Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              📊 Advertising Management
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive ad placement management, revenue tracking, and performance analytics
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <CompactDatePicker
              value={currentDateRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range"
              className="w-full sm:w-56"
            />
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center">
              <Plus className="w-4 h-4 mr-2" />
              New Ad Placement
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${adStats.totalRevenue.toLocaleString()}</p>
              <p className="text-xs text-green-600 mt-1">+12.5% from last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Impressions</p>
              <p className="text-2xl font-bold text-gray-900">{adStats.totalImpressions.toLocaleString()}</p>
              <p className="text-xs text-blue-600 mt-1">+8.3% from last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <Eye className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average CTR</p>
              <p className="text-2xl font-bold text-gray-900">{adStats.averageCTR.toFixed(1)}%</p>
              <p className="text-xs text-green-600 mt-1">+0.4% from last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Ads</p>
              <p className="text-2xl font-bold text-gray-900">{adStats.activeAds}</p>
              <p className="text-xs text-gray-600 mt-1">Total placements</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clicks</p>
              <p className="text-2xl font-bold text-gray-900">{adStats.totalClicks.toLocaleString()}</p>
              <p className="text-xs text-green-600 mt-1">+15.2% from last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average CPM</p>
              <p className="text-2xl font-bold text-gray-900">${adStats.averageCPM.toFixed(2)}</p>
              <p className="text-xs text-blue-600 mt-1">+2.1% from last period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 广告位管理 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                <Eye className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Ad Placement Management</h3>
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={adFilter}
                onChange={(e) => setAdFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="Active">Active</option>
                <option value="Paused">Paused</option>
              </select>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {adPlacements
              .filter(ad => adFilter === 'all' || ad.status === adFilter)
              .map((placement) => (
              <div key={placement.id} className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 hover:shadow-md transition-all duration-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-semibold text-gray-900 text-lg">{placement.name}</h4>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                      placement.status === 'Active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {placement.status}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleToggleAdStatus(placement.id)}
                      className={`px-3 py-1 text-xs font-medium rounded-lg transition-colors ${
                        placement.status === 'Active'
                          ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      }`}
                    >
                      {placement.status === 'Active' ? 'Pause' : 'Activate'}
                    </button>
                    <button
                      onClick={() => handleEditAd(placement.id)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-xs font-medium"
                    >
                      <Edit className="w-3 h-3 inline mr-1" />
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteAd(placement.id)}
                      className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-xs font-medium"
                    >
                      <Trash2 className="w-3 h-3 inline mr-1" />
                      Delete
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500 mb-1">Type</p>
                    <p className="font-medium">{placement.type}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 mb-1">Revenue</p>
                    <p className="font-medium text-green-600">${placement.revenue.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 mb-1">Impressions</p>
                    <p className="font-medium">{placement.impressions.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 mb-1">CTR</p>
                    <p className="font-medium">{placement.ctr}%</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Total Clicks: <span className="font-medium">{placement.clicks.toLocaleString()}</span></span>
                    <span className="text-gray-600">CPM: <span className="font-medium">${(placement.revenue / (placement.impressions / 1000)).toFixed(2)}</span></span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 广告收入趋势图表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Ad Revenue Trend Analysis</h3>
              <p className="text-gray-600">Track advertising performance over time</p>
            </div>
          </div>
        </div>

        <div className="h-80 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={adRevenueData}>
              <defs>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorImpressions" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis dataKey="name" stroke="#6b7280" />
              <YAxis stroke="#6b7280" tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
              <Tooltip
                formatter={(value: number, name: string) => [
                  name === 'revenue' ? `$${value.toLocaleString()}` : value.toLocaleString(),
                  name === 'revenue' ? 'Revenue' :
                  name === 'impressions' ? 'Impressions' :
                  name === 'clicks' ? 'Clicks' : 'CTR'
                ]}
                labelFormatter={(label) => `Period: ${label}`}
              />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#3b82f6"
                fillOpacity={1}
                fill="url(#colorRevenue)"
                strokeWidth={2}
              />
              <Area
                type="monotone"
                dataKey="impressions"
                stroke="#10b981"
                fillOpacity={1}
                fill="url(#colorImpressions)"
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* 汇总统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total Revenue</p>
                <p className="text-lg font-semibold text-blue-900">
                  ${adRevenueData.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <Eye className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">Total Impressions</p>
                <p className="text-lg font-semibold text-green-900">
                  {adRevenueData.reduce((sum, item) => sum + item.impressions, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-900">Average CTR</p>
                <p className="text-lg font-semibold text-purple-900">
                  {(adRevenueData.reduce((sum, item) => sum + item.ctr, 0) / adRevenueData.length).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-orange-900">Total Clicks</p>
                <p className="text-lg font-semibold text-orange-900">
                  {adRevenueData.reduce((sum, item) => sum + (item.clicks || 0), 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑广告位模态框 */}
      {showEditModal && editingAd && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Edit Ad Placement</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ad Name
                  </label>
                  <input
                    type="text"
                    value={editForm.name}
                    onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter ad name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ad Type
                  </label>
                  <select
                    value={editForm.type}
                    onChange={(e) => setEditForm(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Banner Ad">Banner Ad</option>
                    <option value="Display Ad">Display Ad</option>
                    <option value="Native Ad">Native Ad</option>
                    <option value="Mobile Ad">Mobile Ad</option>
                    <option value="Video Ad">Video Ad</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={editForm.status}
                    onChange={(e) => setEditForm(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Active">Active</option>
                    <option value="Paused">Paused</option>
                    <option value="Draft">Draft</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Revenue ($)
                  </label>
                  <input
                    type="number"
                    value={editForm.revenue}
                    onChange={(e) => setEditForm(prev => ({ ...prev, revenue: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              {/* 性能指标 */}
              <div className="border-t pt-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Impressions
                    </label>
                    <input
                      type="number"
                      value={editForm.impressions}
                      onChange={(e) => setEditForm(prev => ({ ...prev, impressions: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CTR (%)
                    </label>
                    <input
                      type="number"
                      value={editForm.ctr}
                      onChange={(e) => setEditForm(prev => ({ ...prev, ctr: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.0"
                      min="0"
                      step="0.1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Clicks
                    </label>
                    <input
                      type="number"
                      value={editForm.clicks}
                      onChange={(e) => setEditForm(prev => ({ ...prev, clicks: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0"
                      min="0"
                    />
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>Save Changes</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvertisingPage;
