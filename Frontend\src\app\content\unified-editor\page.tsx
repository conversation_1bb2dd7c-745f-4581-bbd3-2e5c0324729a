'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Header from '@/components/Header';
import AuthGuard from '@/components/AuthGuard';
import UnifiedMediaEditor from '@/components/UnifiedMediaEditor';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, Save, Eye, Share2 } from 'lucide-react';

/**
 * 统一媒体编辑器演示页面
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、简约设计
 */

interface MediaItem {
  id: string;
  type: 'text' | 'audio' | 'video' | 'image';
  content: any;
  position: number;
}

interface ProjectData {
  title: string;
  description: string;
  content: MediaItem[];
  category: string;
  tags: string;
  isPublic: boolean;
}

function UnifiedEditorContent() {
  const { user } = useSimpleAuth();
  const router = useRouter();

  const [projectData, setProjectData] = useState<ProjectData>({
    title: '',
    description: '',
    content: [],
    category: '',
    tags: '',
    isPublic: true
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'education', label: 'Education' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'business', label: 'Business' },
    { value: 'health', label: 'Health & Fitness' },
    { value: 'travel', label: 'Travel' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'news', label: 'News' }
  ];

  const handleInputChange = (field: keyof ProjectData, value: string | MediaItem[] | boolean) => {
    setProjectData(prev => ({ ...prev, [field]: value }));
  };

  const handleContentChange = (content: MediaItem[]) => {
    handleInputChange('content', content);
  };

  const handleSave = async () => {
    if (!projectData.title.trim()) {
      alert('Please enter a title');
      return;
    }

    if (projectData.content.length === 0) {
      alert('Please add some content');
      return;
    }

    try {
      setIsSaving(true);
      
      // 模拟保存
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      alert('Project saved successfully!');
    } catch (error) {
      console.error('Error saving project:', error);
      alert('Failed to save project. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (!projectData.title.trim() || projectData.content.length === 0) {
      alert('Please fill in title and content before publishing');
      return;
    }

    try {
      setIsSaving(true);
      
      // 模拟发布
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Project published successfully!');
      router.push('/content');
    } catch (error) {
      console.error('Error publishing project:', error);
      alert('Failed to publish project. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>Back</span>
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Unified Media Editor</h1>
                <p className="text-gray-600 mt-1">Create rich multimedia content with text, audio, and video</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsPreviewMode(!isPreviewMode)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isPreviewMode
                    ? 'bg-purple-100 text-purple-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span>Preview</span>
              </button>
              
              <Button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center space-x-2"
                variant="outline"
              >
                <Save className="w-4 h-4" />
                <span>{isSaving ? 'Saving...' : 'Save Draft'}</span>
              </Button>
              
              <Button
                onClick={handlePublish}
                disabled={isSaving}
                className="flex items-center space-x-2"
              >
                <Share2 className="w-4 h-4" />
                <span>Publish</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 左侧项目设置 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Project Settings</h2>
              
              <div className="space-y-4">
                {/* 标题 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={projectData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter project title..."
                  />
                </div>

                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={projectData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="Enter project description..."
                  />
                </div>

                {/* 分类 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={projectData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select category...</option>
                    {categories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 标签 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    value={projectData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter tags separated by commas..."
                  />
                </div>

                {/* 可见性 */}
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={projectData.isPublic}
                      onChange={(e) => handleInputChange('isPublic', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Make this project public</span>
                  </label>
                </div>

                {/* 项目统计 */}
                <div className="pt-4 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Project Stats</h3>
                  <div className="space-y-1 text-sm text-gray-600">
                    <div>Content items: {projectData.content.length}</div>
                    <div>Text blocks: {projectData.content.filter(item => item.type === 'text').length}</div>
                    <div>Media files: {projectData.content.filter(item => item.type !== 'text').length}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧编辑器 */}
          <div className="lg:col-span-3">
            <UnifiedMediaEditor
              initialContent={projectData.content}
              onChange={handleContentChange}
              onSave={handleContentChange}
              className="min-h-[600px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function UnifiedEditorPage() {
  return (
    <AuthGuard>
      <UnifiedEditorContent />
    </AuthGuard>
  );
}
