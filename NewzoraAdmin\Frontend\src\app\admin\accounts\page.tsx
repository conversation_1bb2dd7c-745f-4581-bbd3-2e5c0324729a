'use client';

import React, { useState, useEffect } from 'react';
import { Users, Shield, CheckCircle, XCircle, AlertTriangle, Eye, Edit, Trash2, UserCheck, UserX, Search, Filter } from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';
import { filterByDateRange } from '@/utils/dateFilters';

/**
 * 账户管理页面 - 专门管理用户账户、要素认证和账户安全
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const AccountsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([]);
  const [accountFilter, setAccountFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // 账户统计数据
  const [accountStats, setAccountStats] = useState({
    totalUsers: 12450,
    verifiedUsers: 8920,
    pendingVerification: 1240,
    suspendedUsers: 180,
    newRegistrations: 340,
    activeUsers: 9850
  });

  // 生成用户账户数据
  const generateAccountData = () => {
    const accounts = [];
    const statuses = ['verified', 'pending', 'suspended', 'unverified'];
    const verificationLevels = ['basic', 'intermediate', 'advanced'];
    const countries = ['United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Japan', 'Australia', 'Brazil'];

    for (let i = 1; i <= 100; i++) {
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const verificationLevel = verificationLevels[Math.floor(Math.random() * verificationLevels.length)];
      const country = countries[Math.floor(Math.random() * countries.length)];
      const registrationDate = new Date();
      registrationDate.setDate(registrationDate.getDate() - Math.floor(Math.random() * 365));

      accounts.push({
        id: `user_${i.toString().padStart(4, '0')}`,
        username: `user${i}`,
        email: `user${i}@example.com`,
        fullName: `User ${i}`,
        status,
        verificationLevel,
        country,
        registrationDate: registrationDate.toISOString().split('T')[0],
        lastLogin: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        totalEarnings: Math.floor(Math.random() * 10000),
        documentsSubmitted: status !== 'unverified',
        phoneVerified: Math.random() > 0.3,
        emailVerified: Math.random() > 0.1,
        createdAt: registrationDate.toISOString()
      });
    }

    return accounts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  // 账户数据状态
  const [accounts, setAccounts] = useState(() => generateAccountData());
  const [allAccounts, setAllAccounts] = useState(() => generateAccountData());

  useEffect(() => {
    // 初始化数据
    const allAccountData = generateAccountData();
    setAllAccounts(allAccountData);
    setAccounts(allAccountData.slice(0, 20)); // 显示前20条

    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // 根据日期范围过滤账户记录
    if (allAccounts.length > 0) {
      const filteredAccounts = filterByDateRange(allAccounts, dateRange, {
        dateField: 'createdAt'
      });
      setAccounts(filteredAccounts.slice(0, 20)); // 显示前20条
    }

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    setAccountStats({
      totalUsers: Math.floor(12450 * multiplier),
      verifiedUsers: Math.floor(8920 * multiplier),
      pendingVerification: Math.floor(1240 * multiplier),
      suspendedUsers: Math.floor(180 * multiplier),
      newRegistrations: Math.floor(340 * multiplier),
      activeUsers: Math.floor(9850 * multiplier)
    });
  };

  // 处理账户操作
  const handleVerifyAccount = (accountId: string) => {
    setAccounts(prev => prev.map(account =>
      account.id === accountId
        ? { ...account, status: 'verified', verificationLevel: 'basic' }
        : account
    ));
    alert('Account verified successfully!');
  };

  const handleSuspendAccount = (accountId: string) => {
    const reason = prompt('Please enter suspension reason:');
    if (!reason) return;

    setAccounts(prev => prev.map(account =>
      account.id === accountId
        ? { ...account, status: 'suspended' }
        : account
    ));
    alert('Account suspended successfully!');
  };

  const handleDeleteAccount = (accountId: string) => {
    if (confirm('Are you sure you want to delete this account? This action cannot be undone.')) {
      setAccounts(prev => prev.filter(account => account.id !== accountId));
      alert('Account deleted successfully!');
    }
  };

  // 过滤账户
  const filteredAccounts = accounts.filter(account => {
    const matchesFilter = accountFilter === 'all' || account.status === accountFilter;
    const matchesSearch = !searchQuery ||
      account.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      account.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      account.fullName.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  // 分页逻辑
  const totalPages = Math.ceil(filteredAccounts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedAccounts = filteredAccounts.slice(startIndex, endIndex);

  // 重置页码当过滤条件改变时
  React.useEffect(() => {
    setCurrentPage(1);
  }, [accountFilter, searchQuery]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Account Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-indigo-50">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              👥 Account Management
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive user account management, verification, and security oversight
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <CompactDatePicker
              value={currentDateRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range"
              className="w-full sm:w-56"
            />
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.totalUsers.toLocaleString()}</p>
              <p className="text-xs text-blue-600 mt-1">All registered</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Verified Users</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.verifiedUsers.toLocaleString()}</p>
              <p className="text-xs text-green-600 mt-1">Fully verified</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Verification</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.pendingVerification.toLocaleString()}</p>
              <p className="text-xs text-yellow-600 mt-1">Awaiting review</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Suspended</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.suspendedUsers.toLocaleString()}</p>
              <p className="text-xs text-red-600 mt-1">Account suspended</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
              <XCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">New Registrations</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.newRegistrations.toLocaleString()}</p>
              <p className="text-xs text-purple-600 mt-1">This period</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <UserCheck className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-gray-900">{accountStats.activeUsers.toLocaleString()}</p>
              <p className="text-xs text-teal-600 mt-1">Last 30 days</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by username, email, or name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          <select
            value={accountFilter}
            onChange={(e) => setAccountFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="all">All Status</option>
            <option value="verified">Verified</option>
            <option value="pending">Pending Verification</option>
            <option value="unverified">Unverified</option>
            <option value="suspended">Suspended</option>
          </select>
          <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* 账户管理表格 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <Users className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">User Accounts</h3>
            </div>
            <div className="text-sm text-gray-600 bg-white px-3 py-2 rounded-lg border border-gray-200 shadow-sm">
              Showing {startIndex + 1} to {Math.min(endIndex, filteredAccounts.length)} of {filteredAccounts.length} accounts
            </div>
          </div>
        </div>

        <div className="overflow-x-auto max-w-full">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedAccounts(filteredAccounts.map(a => a.id));
                      } else {
                        setSelectedAccounts([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                  User
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                  Verification
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  Country
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Earnings
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                  Registration
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {paginatedAccounts.map((account, index) => (
                <tr key={account.id} className={`hover:bg-blue-50 transition-colors duration-200 ${
                  index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                }`}>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedAccounts.includes(account.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAccounts(prev => [...prev, account.id]);
                        } else {
                          setSelectedAccounts(prev => prev.filter(id => id !== account.id));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                    />
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-7 w-7">
                        <div className="h-7 w-7 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center">
                          <span className="text-white font-medium text-xs">
                            {account.fullName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3 min-w-0 flex-1">
                        <div className="text-sm font-medium text-gray-900 truncate">{account.fullName}</div>
                        <div className="text-xs text-gray-500 truncate">@{account.username}</div>
                        <div className="text-xs text-gray-400 truncate">{account.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      account.status === 'verified' ? 'bg-green-100 text-green-800' :
                      account.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      account.status === 'suspended' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {account.status.charAt(0).toUpperCase() + account.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${
                        account.verificationLevel === 'advanced' ? 'bg-purple-100 text-purple-800' :
                        account.verificationLevel === 'intermediate' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {account.verificationLevel.charAt(0).toUpperCase() + account.verificationLevel.slice(1)}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {account.emailVerified && <span className="text-green-600">✓</span>}
                      {account.phoneVerified && <span className="text-green-600 ml-1">✓</span>}
                      {account.documentsSubmitted && <span className="text-green-600 ml-1">✓</span>}
                    </div>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                    {account.country}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">${account.totalEarnings.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">Total</div>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{new Date(account.registrationDate).toLocaleDateString('en-US')}</div>
                    <div className="text-xs text-gray-500">Login: {new Date(account.lastLogin).toLocaleDateString('en-US')}</div>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => alert(`Viewing details for ${account.fullName}`)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {account.status === 'pending' && (
                        <button
                          onClick={() => handleVerifyAccount(account.id)}
                          className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50 transition-colors"
                          title="Verify Account"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      {account.status !== 'suspended' && (
                        <button
                          onClick={() => handleSuspendAccount(account.id)}
                          className="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50 transition-colors"
                          title="Suspend Account"
                        >
                          <UserX className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleDeleteAccount(account.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                        title="Delete Account"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {paginatedAccounts.length === 0 && filteredAccounts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No accounts found matching the current filters.</p>
          </div>
        )}

        {/* 分页组件 */}
        {filteredAccounts.length > 0 && (
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-blue-50 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                ← Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage >= totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-blue-50 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Next →
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 bg-white px-3 py-2 rounded-lg border border-gray-200 shadow-sm">
                  Showing{' '}
                  <span className="font-semibold text-purple-600">{startIndex + 1}</span>{' '}
                  to{' '}
                  <span className="font-semibold text-purple-600">{Math.min(endIndex, filteredAccounts.length)}</span>{' '}
                  of <span className="font-semibold text-purple-600">{filteredAccounts.length}</span> accounts
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage <= 1}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium bg-white hover:bg-blue-50 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
                >
                  ← Previous
                </button>

                {/* 页码显示 */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber;

                    if (totalPages <= 5) {
                      pageNumber = i + 1;
                    } else if (currentPage <= 3) {
                      pageNumber = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => setCurrentPage(pageNumber)}
                        className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
                          pageNumber === currentPage
                            ? 'bg-purple-600 text-white shadow-md'
                            : 'bg-white text-gray-700 border border-gray-300 hover:bg-purple-50 hover:border-purple-300'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage >= totalPages}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium bg-white hover:bg-blue-50 hover:border-blue-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
                >
                  Next →
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountsPage;
