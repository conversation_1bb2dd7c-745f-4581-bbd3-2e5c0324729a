'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Eye, Edit, Trash2, AlertTriangle, Calendar } from 'lucide-react';
import DataTable from '@/components/admin/common/DataTable';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';
import { supabaseService } from '@/services/supabaseService';
import { DataTableColumn } from '@/types/admin';
import { filterByDateRange } from '@/utils/dateFilters';
import { useDataOperations } from '@/contexts/DataSyncContext';
import Link from 'next/link';

interface ContentItem {
  id: string;
  title: string;
  content?: string;
  author_id: string;
  category: string;
  published: boolean;
  views: number;
  likes: number;
  created_at: string;
  type: 'article' | 'video' | 'audio'; // 内容类型
  duration?: string; // 视频/音频时长
  fileSize?: string; // 文件大小
  thumbnail?: string; // 缩略图
  profiles: {
    username: string;
    display_name: string;
  };
}

const ContentPage: React.FC = () => {
  const { removeContent } = useDataOperations();
  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [allContentItems, setAllContentItems] = useState<ContentItem[]>([]); // Store unfiltered data
  const [loading, setLoading] = useState(true);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState(''); // 新增类型过滤器
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });

  const columns: DataTableColumn<ContentItem>[] = [
    {
      key: 'title',
      title: 'Content Info',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {record.type === 'video' && (
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-red-600 text-xs font-medium">VIDEO</span>
              </div>
            )}
            {record.type === 'audio' && (
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xs font-medium">AUDIO</span>
              </div>
            )}
            {record.type === 'article' && (
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xs font-medium">TEXT</span>
              </div>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900 truncate max-w-xs">{record.title}</div>
            <div className="text-sm text-gray-500">
              Author: {record.profiles?.display_name || record.profiles?.username}
              {record.duration && <span className="ml-2">• {record.duration}</span>}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
          {value || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'published',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? 'Published' : 'Draft'}
        </span>
      )
    },
    {
      key: 'views',
      title: 'Statistics',
      render: (_, record) => (
        <div className="text-sm text-gray-900">
          <div>Views: {record.views || 0}</div>
          <div>Likes: {record.likes || 0}</div>
        </div>
      )
    },
    {
      key: 'created_at',
      title: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString('en-US')
    },
    {
      key: 'id',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <button 
            onClick={(e) => {
              e.preventDefault();
              alert(`View article: ${record.title}`);
            }}
            className="text-blue-600 hover:text-blue-900 text-sm p-1"
            title="View"
          >
            <Eye className="w-4 h-4" />
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              alert(`Edit article: ${record.title}`);
            }}
            className="text-green-600 hover:text-green-900 text-sm p-1"
            title="Edit"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              handleTogglePublish(record.id, record.published);
            }}
            className={`text-sm px-2 py-1 rounded ${
              record.published 
                ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200' 
                : 'bg-green-100 text-green-600 hover:bg-green-200'
            }`}
          >
            {record.published ? 'Unpublish' : 'Publish'}
          </button>
          <button 
            onClick={(e) => {
              e.preventDefault();
              if (confirm(`Are you sure you want to delete "${record.title}"?`)) {
                // 从当前数据中移除
                setContentItems(prev => prev.filter(item => item.id !== record.id));
                setAllContentItems(prev => prev.filter(item => item.id !== record.id));

                // 更新分页信息
                setPagination(prev => ({ ...prev, total: prev.total - 1 }));

                // 同步更新全局统计数据
                removeContent(1);

                alert('Content has been deleted');
              }
            }}
            className="text-red-600 hover:text-red-900 text-sm p-1"
            title="Delete"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ];

  // 生成模拟数据，包含文章、视频、音频
  const generateMockContent = (): ContentItem[] => {
    const authors = [
      { username: 'john_doe', display_name: 'John Doe' },
      { username: 'jane_smith', display_name: 'Jane Smith' },
      { username: 'mike_wilson', display_name: 'Mike Wilson' },
      { username: 'sarah_jones', display_name: 'Sarah Jones' },
      { username: 'david_brown', display_name: 'David Brown' }
    ];

    // 与前台主站一致的类别配置
    const categories = [
      'Technology', 'Science', 'Health', 'Business', 'Finance',
      'Education', 'Lifestyle', 'Travel', 'Food', 'Fashion',
      'Sports', 'Entertainment', 'Gaming', 'Art', 'Music',
      'Photography', 'Writing', 'Politics', 'Environment', 'Culture'
    ];
    const contentTypes: ('article' | 'video' | 'audio')[] = ['article', 'video', 'audio'];

    const mockContent: ContentItem[] = [];

    for (let i = 0; i < 50; i++) {
      const type = contentTypes[Math.floor(Math.random() * contentTypes.length)];
      const author = authors[Math.floor(Math.random() * authors.length)];
      const category = categories[Math.floor(Math.random() * categories.length)];
      const createdDate = new Date();
      createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 365));

      let title = '';
      let duration = undefined;
      let fileSize = undefined;

      if (type === 'article') {
        title = `Article: ${category} Topic ${i + 1}`;
      } else if (type === 'video') {
        title = `Video: ${category} Tutorial ${i + 1}`;
        duration = `${Math.floor(Math.random() * 30) + 1}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
        fileSize = `${Math.floor(Math.random() * 500) + 50}MB`;
      } else {
        title = `Podcast: ${category} Discussion ${i + 1}`;
        duration = `${Math.floor(Math.random() * 60) + 10}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
        fileSize = `${Math.floor(Math.random() * 100) + 10}MB`;
      }

      mockContent.push({
        id: (i + 1).toString(),
        title,
        content: type === 'article' ? `Content for ${title}` : `${type} content for ${title}`,
        author_id: author?.username || 'unknown',
        category: category || 'Technology',
        type: type || 'article',
        duration: duration || '0:00',
        fileSize: fileSize || '0MB',
        published: Math.random() > 0.3,
        views: Math.floor(Math.random() * 10000),
        likes: Math.floor(Math.random() * 1000),
        created_at: createdDate.toISOString(),
        profiles: author || { username: 'unknown', display_name: 'Unknown User' }
      });
    }

    return mockContent.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  };

  const fetchContent = async () => {
    try {
      setLoading(true);

      // 使用模拟数据，实际项目中应该从API获取
      const data = generateMockContent();
      setAllContentItems(data);

      // Apply filters
      let filteredData = data;

      if (searchQuery) {
        filteredData = filteredData.filter(item =>
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (item.content && item.content.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      }

      if (categoryFilter) {
        filteredData = filteredData.filter(item => item.category === categoryFilter);
      }

      if (statusFilter) {
        const isPublished = statusFilter === 'published';
        filteredData = filteredData.filter(item => item.published === isPublished);
      }

      if (typeFilter) {
        filteredData = filteredData.filter(item => item.type === typeFilter);
      }

      if (currentDateRange) {
        filteredData = filterByDateRange(filteredData, currentDateRange, {
          dateField: 'created_at'
        });
      }

      setContentItems(filteredData);
      setPagination(prev => ({
        ...prev,
        total: filteredData.length
      }));
    } catch (error) {
      console.error('Failed to fetch content:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);
    setPagination(prev => ({ ...prev, current: 1 })); // Reset to first page
  };

  useEffect(() => {
    fetchContent();
  }, [pagination.current, searchQuery, categoryFilter, statusFilter, typeFilter, currentDateRange]);

  const handleBatchDelete = async () => {
    if (selectedArticles.length === 0) {
      alert('Please select items to delete');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedArticles.length} items?`)) {
      return;
    }

    try {
      setLoading(true);

      // 模拟API调用 - 实际项目中应该调用真实的API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 从当前数据中移除选中的项目
      const updatedItems = contentItems.filter(item => !selectedArticles.includes(item.id));
      const updatedAllItems = allContentItems.filter(item => !selectedArticles.includes(item.id));

      setContentItems(updatedItems);
      setAllContentItems(updatedAllItems);

      // 更新分页信息
      setPagination(prev => ({
        ...prev,
        total: prev.total - selectedArticles.length
      }));

      // 清空选择
      setSelectedArticles([]);

      // 同步更新全局统计数据
      removeContent(selectedArticles.length);

      alert(`Successfully deleted ${selectedArticles.length} items`);
    } catch (error) {
      console.error('Failed to delete items:', error);
      alert('Failed to delete items. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchContent();
  };

  const handleTogglePublish = async (articleId: string, currentStatus: boolean) => {
    try {
      await supabaseService.updateArticleStatus(articleId, !currentStatus);
      // Update local data
      setContentItems(prev => prev.map(item =>
        item.id === articleId ? { ...item, published: !currentStatus } : item
      ));
      alert(`Article has been ${!currentStatus ? 'published' : 'unpublished'}`);
    } catch (error) {
      console.error('Failed to update article status:', error);
      alert('Operation failed, please try again');
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Content Management</h1>
        <div className="flex space-x-3">
          {/* Add Report Management Link */}
          <Link href="/admin/content/reports" className="btn-secondary flex items-center">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Report Management
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search content titles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Types</option>
            <option value="article">Articles</option>
            <option value="video">Videos</option>
            <option value="audio">Audio</option>
          </select>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="form-select w-40"
          >
            <option value="">All Categories</option>
            <option value="Technology">Technology</option>
            <option value="Science">Science</option>
            <option value="Health">Health</option>
            <option value="Business">Business</option>
            <option value="Finance">Finance</option>
            <option value="Education">Education</option>
            <option value="Lifestyle">Lifestyle</option>
            <option value="Travel">Travel</option>
            <option value="Food">Food</option>
            <option value="Fashion">Fashion</option>
            <option value="Sports">Sports</option>
            <option value="Entertainment">Entertainment</option>
            <option value="Gaming">Gaming</option>
            <option value="Art">Art</option>
            <option value="Music">Music</option>
            <option value="Photography">Photography</option>
            <option value="Writing">Writing</option>
            <option value="Politics">Politics</option>
            <option value="Environment">Environment</option>
            <option value="Culture">Culture</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
          </select>
          <CompactDatePicker
            value={currentDateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            className="w-48"
          />
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* Bulk Actions */}
      {selectedArticles.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedArticles.length} articles
            </span>
            <div className="flex space-x-2">
              <button className="btn-secondary text-sm">Bulk Publish</button>
              <button className="btn-secondary text-sm">Bulk Unpublish</button>
              <button
                onClick={handleBatchDelete}
                className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete ({selectedArticles.length})
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Content List */}
      <DataTable
        data={contentItems}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        rowSelection={{
          selectedRowKeys: selectedArticles,
          onChange: setSelectedArticles
        }}
      />
    </div>
  );
};

export default ContentPage;