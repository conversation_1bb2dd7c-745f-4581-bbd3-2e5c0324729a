'use client';

import React from 'react';

interface ChartData {
  date: string;
  revenue: number;
  withdrawals: number;
  netRevenue: number;
}

interface SimpleChartProps {
  data: ChartData[];
  height?: number;
}

/**
 * 简单的CSS图表组件 - 作为Recharts的备用方案
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const SimpleChart: React.FC<SimpleChartProps> = ({ data, height = 320 }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-gray-500">No data available</p>
        </div>
      </div>
    );
  }

  // 计算最大值用于缩放，添加安全检查
  const maxValue = Math.max(
    1, // 确保最小值为1，避免除零错误
    ...data.map(d => Math.max(
      Math.abs(d.revenue || 0),
      Math.abs(d.withdrawals || 0),
      Math.abs(d.netRevenue || 0)
    ))
  );

  // 格式化数值
  const formatValue = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}k`;
    }
    return `$${value}`;
  };

  // 格式化日期，添加错误处理
  const formatDate = (dateStr: string): string => {
    try {
      if (dateStr && dateStr.includes('-')) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }
      }
      return dateStr || 'N/A';
    } catch {
      return dateStr || 'N/A';
    }
  };

  return (
    <div className="w-full" style={{ height }}>
      {/* 图例 */}
      <div className="flex justify-center space-x-6 mb-4">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Total Revenue</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Withdrawals</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Net Revenue</span>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="relative bg-white border border-gray-200 rounded-lg p-4" style={{ height: height - 60 }}>
        {/* Y轴标签 */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-2">
          <span>{formatValue(maxValue)}</span>
          <span>{formatValue(maxValue * 0.75)}</span>
          <span>{formatValue(maxValue * 0.5)}</span>
          <span>{formatValue(maxValue * 0.25)}</span>
          <span>$0</span>
        </div>

        {/* 图表内容 */}
        <div className="ml-12 h-full relative">
          {/* 网格线 */}
          <div className="absolute inset-0">
            {[0, 25, 50, 75, 100].map((percent) => (
              <div
                key={percent}
                className="absolute w-full border-t border-gray-100"
                style={{ top: `${100 - percent}%` }}
              />
            ))}
          </div>

          {/* 数据点和线条 */}
          <div className="relative h-full flex items-end justify-between">
            {data.map((item, index) => {
              const revenueHeight = (item.revenue / maxValue) * 100;
              const withdrawalsHeight = (item.withdrawals / maxValue) * 100;
              const netRevenueHeight = (item.netRevenue / maxValue) * 100;

              return (
                <div key={index} className="flex flex-col items-center group relative">
                  {/* 数据点 */}
                  <div className="relative mb-2" style={{ height: '200px' }}>
                    {/* Revenue点 */}
                    <div
                      className="absolute w-2 h-2 bg-blue-500 rounded-full transform -translate-x-1/2"
                      style={{ bottom: `${revenueHeight}%`, left: '50%' }}
                    />
                    {/* Withdrawals点 */}
                    <div
                      className="absolute w-2 h-2 bg-red-500 rounded-full transform -translate-x-1/2"
                      style={{ bottom: `${withdrawalsHeight}%`, left: '50%' }}
                    />
                    {/* Net Revenue点 */}
                    <div
                      className="absolute w-2 h-2 bg-green-500 rounded-full transform -translate-x-1/2"
                      style={{ bottom: `${netRevenueHeight}%`, left: '50%' }}
                    />
                  </div>

                  {/* X轴标签 */}
                  <div className="text-xs text-gray-500 text-center">
                    {formatDate(item.date)}
                  </div>

                  {/* 悬停提示 */}
                  <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    <div>Revenue: {formatValue(item.revenue)}</div>
                    <div>Withdrawals: {formatValue(item.withdrawals)}</div>
                    <div>Net: {formatValue(item.netRevenue)}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleChart;
