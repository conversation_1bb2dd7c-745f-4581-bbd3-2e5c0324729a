'use client';

import React from 'react';
import Link from 'next/link';
import { CheckCircle, ExternalLink, DollarSign, Eye, CreditCard, UserCheck } from 'lucide-react';

/**
 * 导航测试页面 - 测试所有新创建的页面是否能正常访问
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const TestNavigationPage: React.FC = () => {
  const pages = [
    {
      title: '💰 Revenue Overview',
      description: 'Comprehensive revenue tracking and financial analytics dashboard',
      href: '/admin/monetization',
      icon: <DollarSign className="w-6 h-6" />,
      color: 'from-blue-500 to-indigo-600'
    },
    {
      title: '📊 Advertising Management',
      description: 'Manage ad placements, revenue tracking, and performance analytics',
      href: '/admin/advertising',
      icon: <Eye className="w-6 h-6" />,
      color: 'from-indigo-500 to-purple-600'
    },
    {
      title: '💳 Withdrawal Management',
      description: 'Process user withdrawals, account management, and payment analytics',
      href: '/admin/withdrawals',
      icon: <CreditCard className="w-6 h-6" />,
      color: 'from-green-500 to-emerald-600'
    },
    {
      title: '👥 Account Management',
      description: 'User account management, verification, and security oversight',
      href: '/admin/accounts',
      icon: <UserCheck className="w-6 h-6" />,
      color: 'from-purple-500 to-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          🧪 Navigation Test
        </h1>
        <p className="text-lg text-gray-600">
          Test all newly created admin pages to ensure they are working correctly
        </p>
      </div>

      {/* 状态指示器 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
        <div className="flex items-center">
          <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">All Systems Operational</h3>
            <p className="text-gray-600">All pages have been successfully created and are ready for testing</p>
          </div>
        </div>
      </div>

      {/* 页面卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {pages.map((page, index) => (
          <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-200">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-12 h-12 bg-gradient-to-br ${page.color} rounded-xl flex items-center justify-center mr-4 text-white`}>
                  {page.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{page.title}</h3>
                  <p className="text-gray-600 text-sm mt-1">{page.description}</p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Route: <code className="bg-gray-100 px-2 py-1 rounded">{page.href}</code>
              </div>
              <Link
                href={page.href}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 text-sm font-medium"
              >
                <span>Visit Page</span>
                <ExternalLink className="w-4 h-4 ml-2" />
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* 功能特性总结 */}
      <div className="mt-12 bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">✅ Completed Features</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🔧 Technical Improvements</h3>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Fixed syntax errors in monetization page
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Refactored date range functionality to calendar table
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Removed quick filter options, unified calendar selection
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Updated SafeDatePicker component
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📄 New Pages Created</h3>
            <ul className="space-y-2 text-gray-600">
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Separated advertising management
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Separated withdrawal management
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Created account management category
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                Updated navigation menu structure
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-semibold text-blue-900 mb-2">📋 Code Quality Standards</h4>
          <p className="text-sm text-blue-800">
            All pages follow the project coding rules: TypeScript strong typing, Tailwind CSS styling, 
            complete error handling, functional components, responsive design, and calendar table selection.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TestNavigationPage;
