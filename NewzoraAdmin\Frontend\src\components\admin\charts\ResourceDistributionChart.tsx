'use client';

import React from 'react';

interface ResourceData {
  name: string;
  value: number;
  color: string;
}

interface ResourceDistributionChartProps {
  data: ResourceData[];
  height?: number;
}

/**
 * 资源分布图表组件 - 使用CSS实现的简单饼图
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const ResourceDistributionChart: React.FC<ResourceDistributionChartProps> = ({ data, height = 320 }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
            </svg>
          </div>
          <p className="text-gray-500">No resource data available</p>
        </div>
      </div>
    );
  }

  // 计算总值
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  // 如果总值为0，显示空状态
  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
            </svg>
          </div>
          <p className="text-gray-500">No resource usage data</p>
        </div>
      </div>
    );
  }

  // 计算每个项目的百分比和累积角度
  let cumulativePercentage = 0;
  const processedData = data.map(item => {
    const percentage = (item.value / total) * 100;
    const startAngle = cumulativePercentage * 3.6; // 转换为度数
    cumulativePercentage += percentage;
    const endAngle = cumulativePercentage * 3.6;
    
    return {
      ...item,
      percentage,
      startAngle,
      endAngle
    };
  });

  // 创建SVG路径
  const createArcPath = (centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(centerX, centerY, radius, endAngle);
    const end = polarToCartesian(centerX, centerY, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
      "M", centerX, centerY,
      "L", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
      "Z"
    ].join(" ");
  };

  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  // 使用更小的尺寸以适应界面
  const centerX = 80;
  const centerY = 80;
  const radius = 60;

  return (
    <div className="w-full" style={{ height }}>
      <div className="flex flex-col items-center space-y-4">
        {/* 饼图 - 更紧凑的设计 */}
        <div className="relative">
          <svg width="160" height="160" className="transform -rotate-90">
            {processedData.map((item, index) => (
              <path
                key={index}
                d={createArcPath(centerX, centerY, radius, item.startAngle, item.endAngle)}
                fill={item.color}
                stroke="white"
                strokeWidth="2"
                className="hover:opacity-80 transition-opacity duration-200"
              />
            ))}
            {/* 中心圆 */}
            <circle
              cx={centerX}
              cy={centerY}
              r="30"
              fill="white"
              stroke="#e5e7eb"
              strokeWidth="2"
            />
          </svg>

          {/* 中心文本 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">100%</div>
              <div className="text-xs text-gray-500">全部的</div>
            </div>
          </div>
        </div>

        {/* 图例 - 更紧凑的布局 */}
        <div className="grid grid-cols-1 gap-2 w-full">
          {processedData.map((item, index) => (
            <div key={index} className="flex items-center justify-between px-2">
              <div className="flex items-center space-x-2 flex-1">
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-xs font-medium text-gray-700 truncate">{item.name}</span>
              </div>
              <span className="text-xs text-gray-500 font-medium">{item.percentage.toFixed(1)}%</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ResourceDistributionChart;
