'use client';

import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, AlertTriangle, Eye, FileText, Upload, Download, Search, Filter, User, Shield, Calendar } from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';

/**
 * 账户验证管理页面 - 管理用户身份验证和KYC流程
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */

interface VerificationRequest {
  id: string;
  userId: string;
  username: string;
  email: string;
  fullName: string;
  verificationType: 'identity' | 'address' | 'income' | 'business';
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  documents: {
    type: string;
    filename: string;
    uploadedAt: string;
  }[];
  notes?: string;
  rejectionReason?: string;
}

const AccountVerificationPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [verificationRequests, setVerificationRequests] = useState<VerificationRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<VerificationRequest[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // 验证统计数据
  const [verificationStats, setVerificationStats] = useState({
    totalRequests: 1250,
    pendingRequests: 340,
    approvedRequests: 780,
    rejectedRequests: 130,
    todaySubmissions: 45,
    averageProcessingTime: 2.5
  });

  // 生成验证请求数据
  const generateVerificationData = (): VerificationRequest[] => {
    const requests: VerificationRequest[] = [];
    const types: VerificationRequest['verificationType'][] = ['identity', 'address', 'income', 'business'];
    const statuses: VerificationRequest['status'][] = ['pending', 'approved', 'rejected', 'under_review'];
    const documentTypes = ['passport', 'driver_license', 'utility_bill', 'bank_statement', 'tax_return', 'business_license'];

    for (let i = 1; i <= 100; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const submittedDate = new Date();
      submittedDate.setDate(submittedDate.getDate() - Math.floor(Math.random() * 30));

      const documents = [];
      const numDocs = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < numDocs; j++) {
        documents.push({
          type: documentTypes[Math.floor(Math.random() * documentTypes.length)],
          filename: `document_${i}_${j + 1}.pdf`,
          uploadedAt: submittedDate.toISOString()
        });
      }

      requests.push({
        id: `VER${i.toString().padStart(4, '0')}`,
        userId: `user_${i.toString().padStart(4, '0')}`,
        username: `user${i}`,
        email: `user${i}@example.com`,
        fullName: `User ${i}`,
        verificationType: type,
        status,
        submittedAt: submittedDate.toISOString(),
        reviewedAt: status !== 'pending' ? new Date(submittedDate.getTime() + Math.random() * 86400000 * 3).toISOString() : undefined,
        reviewedBy: status !== 'pending' ? '<EMAIL>' : undefined,
        documents,
        notes: Math.random() > 0.7 ? 'Additional verification required' : undefined,
        rejectionReason: status === 'rejected' ? 'Document quality insufficient' : undefined
      });
    }

    return requests.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      const data = generateVerificationData();
      setVerificationRequests(data);
      setFilteredRequests(data);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 过滤和搜索逻辑
  useEffect(() => {
    let filtered = verificationRequests;

    // 搜索过滤
    if (searchQuery) {
      filtered = filtered.filter(request =>
        request.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        request.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        request.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        request.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    // 类型过滤
    if (typeFilter !== 'all') {
      filtered = filtered.filter(request => request.verificationType === typeFilter);
    }

    // 日期范围过滤
    if (currentDateRange) {
      filtered = filtered.filter(request => {
        const requestDate = new Date(request.submittedAt);
        return requestDate >= currentDateRange.startDate && requestDate <= currentDateRange.endDate;
      });
    }

    setFilteredRequests(filtered);
    setCurrentPage(1);
  }, [verificationRequests, searchQuery, statusFilter, typeFilter, currentDateRange]);

  // 分页逻辑
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedRequests = filteredRequests.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredRequests.length / itemsPerPage);

  // 状态样式
  const getStatusStyle = (status: VerificationRequest['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'under_review':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 类型样式
  const getTypeStyle = (type: VerificationRequest['verificationType']) => {
    switch (type) {
      case 'identity':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'address':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'income':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'business':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 处理验证请求
  const handleVerificationAction = async (requestId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      setVerificationRequests(prev => prev.map(request =>
        request.id === requestId
          ? {
              ...request,
              status: action === 'approve' ? 'approved' : 'rejected',
              reviewedAt: new Date().toISOString(),
              reviewedBy: '<EMAIL>',
              rejectionReason: action === 'reject' ? reason : undefined
            }
          : request
      ));

      // 这里应该调用实际的API
      console.log(`${action} verification request ${requestId}`, reason);
    } catch (error) {
      console.error('Failed to process verification request:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Account Verification</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 rounded-2xl shadow-xl p-8 text-white">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
              🔍 Account Verification
            </h1>
            <p className="text-lg text-purple-100">
              Manage user identity verification and KYC processes
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4 lg:mt-0">
            <CompactDatePicker
              value={currentDateRange}
              onChange={setCurrentDateRange}
              placeholder="Select date range"
              className="bg-white/10 border-white/20 text-white placeholder-white/70 w-56"
            />
            <button
              onClick={() => {
                // 批量操作功能
                const pendingCount = verificationRequests.filter(req => req.status === 'pending').length;
                if (pendingCount > 0) {
                  if (confirm(`Approve all ${pendingCount} pending verification requests?`)) {
                    setVerificationRequests(prev => prev.map(req =>
                      req.status === 'pending'
                        ? {
                            ...req,
                            status: 'approved' as const,
                            reviewedAt: new Date().toISOString(),
                            reviewedBy: '<EMAIL>'
                          }
                        : req
                    ));
                    alert(`${pendingCount} verification requests approved successfully!`);
                  }
                } else {
                  alert('No pending verification requests to approve.');
                }
              }}
              className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors text-sm font-medium"
            >
              Batch Approve
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.totalRequests.toLocaleString()}</p>
              <p className="text-xs text-blue-600 mt-1">All time</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <FileText className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.pendingRequests.toLocaleString()}</p>
              <p className="text-xs text-yellow-600 mt-1">Awaiting action</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.approvedRequests.toLocaleString()}</p>
              <p className="text-xs text-green-600 mt-1">Verified users</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.rejectedRequests.toLocaleString()}</p>
              <p className="text-xs text-red-600 mt-1">Failed verification</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
              <XCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Today's Submissions</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.todaySubmissions.toLocaleString()}</p>
              <p className="text-xs text-purple-600 mt-1">New requests</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <Upload className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Processing</p>
              <p className="text-2xl font-bold text-gray-900">{verificationStats.averageProcessingTime}d</p>
              <p className="text-xs text-indigo-600 mt-1">Processing time</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Calendar className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by username, email, or request ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="under_review">Under Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="all">All Types</option>
            <option value="identity">Identity</option>
            <option value="address">Address</option>
            <option value="income">Income</option>
            <option value="business">Business</option>
          </select>
          <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* 验证请求表格 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <Shield className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Verification Requests</h3>
            </div>
            <div className="text-sm text-gray-600 bg-white px-3 py-2 rounded-lg border border-gray-200 shadow-sm">
              Showing {startIndex + 1} to {Math.min(endIndex, filteredRequests.length)} of {filteredRequests.length} requests
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request ID</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documents</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{request.id}</div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mr-3">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{request.fullName}</div>
                        <div className="text-sm text-gray-500">{request.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getTypeStyle(request.verificationType)}`}>
                      {request.verificationType.charAt(0).toUpperCase() + request.verificationType.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusStyle(request.status)}`}>
                      {request.status.replace('_', ' ').charAt(0).toUpperCase() + request.status.replace('_', ' ').slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{request.documents.length} files</div>
                    <div className="text-xs text-gray-500">
                      {request.documents.map(doc => doc.type).join(', ')}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {new Date(request.submittedAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(request.submittedAt).toLocaleTimeString()}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {/* 查看详情按钮 */}
                      <button
                        onClick={() => {
                          alert(`Viewing details for ${request.username}'s ${request.verificationType} verification\n\nDocuments: ${request.documents.map(d => d.type).join(', ')}\nSubmitted: ${request.submittedAt}\nStatus: ${request.status}`);
                        }}
                        className="flex items-center px-2 py-1 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 transition-colors text-xs"
                        title="View Details"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        View
                      </button>

                      {/* 待审核状态的操作 */}
                      {request.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleVerificationAction(request.id, 'approve')}
                            className="flex items-center px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors text-xs"
                            title="Approve verification"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Approve
                          </button>
                          <button
                            onClick={() => {
                              const reason = prompt('Enter rejection reason:');
                              if (reason) {
                                handleVerificationAction(request.id, 'reject', reason);
                              }
                            }}
                            className="flex items-center px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors text-xs"
                            title="Reject verification"
                          >
                            <XCircle className="w-3 h-3 mr-1" />
                            Reject
                          </button>
                        </>
                      )}

                      {/* 已批准状态的操作 */}
                      {request.status === 'approved' && (
                        <button
                          onClick={() => {
                            if (confirm('Are you sure you want to revoke this approval?')) {
                              handleVerificationAction(request.id, 'reject', 'Approval revoked by admin');
                            }
                          }}
                          className="flex items-center px-2 py-1 bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors text-xs"
                          title="Revoke approval"
                        >
                          <XCircle className="w-3 h-3 mr-1" />
                          Revoke
                        </button>
                      )}

                      {/* 已拒绝状态的操作 */}
                      {request.status === 'rejected' && (
                        <button
                          onClick={() => {
                            if (confirm('Reset this verification to pending status?')) {
                              setVerificationRequests(prev => prev.map(req => {
                                if (req.id === request.id) {
                                  const updatedReq: VerificationRequest = {
                                    ...req,
                                    status: 'pending',
                                  };
                                  // 删除可选属性
                                  delete (updatedReq as any).rejectionReason;
                                  delete (updatedReq as any).reviewedAt;
                                  delete (updatedReq as any).reviewedBy;
                                  return updatedReq;
                                }
                                return req;
                              }));
                            }
                          }}
                          className="flex items-center px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-xs"
                          title="Reset to pending"
                        >
                          <Clock className="w-3 h-3 mr-1" />
                          Reset
                        </button>
                      )}

                      {/* 审核中状态的操作 */}
                      {request.status === 'under_review' && (
                        <>
                          <button
                            onClick={() => handleVerificationAction(request.id, 'approve')}
                            className="flex items-center px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors text-xs"
                            title="Complete review and approve"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Approve
                          </button>
                          <button
                            onClick={() => {
                              const reason = prompt('Enter rejection reason:');
                              if (reason) {
                                handleVerificationAction(request.id, 'reject', reason);
                              }
                            }}
                            className="flex items-center px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors text-xs"
                            title="Complete review and reject"
                          >
                            <XCircle className="w-3 h-3 mr-1" />
                            Reject
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {startIndex + 1} to {Math.min(endIndex, filteredRequests.length)} of {filteredRequests.length} results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm font-medium text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {paginatedRequests.length === 0 && filteredRequests.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">No verification requests found</p>
            <p className="text-sm">Try adjusting your search criteria or date range.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountVerificationPage;
