'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, X, ChevronLeft, ChevronRight } from 'lucide-react';

export interface DateRange {
  startDate: Date;
  endDate: Date;
  label: string;
}

interface CompactDatePickerProps {
  value: DateRange | null;
  onChange: (dateRange: DateRange) => void;
  className?: string;
  placeholder?: string;
}

/**
 * 紧凑型日期选择器 - 使用日历表格式，减少空间占用
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const CompactDatePicker: React.FC<CompactDatePickerProps> = ({
  value,
  onChange,
  className = '',
  placeholder = 'Select date range'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedStart, setSelectedStart] = useState<Date | null>(null);
  const [selectedEnd, setSelectedEnd] = useState<Date | null>(null);
  const [isSelectingEnd, setIsSelectingEnd] = useState(false);

  // 同步外部值到内部状态
  useEffect(() => {
    if (value) {
      setSelectedStart(value.startDate);
      setSelectedEnd(value.endDate);
      setCurrentMonth(value.startDate);
    } else {
      setSelectedStart(null);
      setSelectedEnd(null);
      setIsSelectingEnd(false);
    }
  }, [value]);

  // 格式化显示值
  const formatDisplayValue = (): string => {
    if (!value) return placeholder;
    
    const start = value.startDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    
    const end = value.endDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
    
    const days = Math.ceil((value.endDate.getTime() - value.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    if (start === end) return `${start} (1 day)`;
    return `${start} - ${end} (${days} days)`;
  };

  // 处理日期点击
  const handleDateClick = (date: Date) => {
    if (!selectedStart || isSelectingEnd) {
      if (!selectedStart) {
        setSelectedStart(date);
        setIsSelectingEnd(true);
      } else {
        // 确保结束日期不早于开始日期
        const finalStart = date < selectedStart ? date : selectedStart;
        const finalEnd = date < selectedStart ? selectedStart : date;
        
        setSelectedEnd(finalEnd);
        setIsSelectingEnd(false);
        
        const newRange: DateRange = {
          startDate: finalStart,
          endDate: finalEnd,
          label: 'Custom Range'
        };
        
        onChange(newRange);
        setIsOpen(false);
      }
    } else {
      // 重新开始选择
      setSelectedStart(date);
      setSelectedEnd(null);
      setIsSelectingEnd(true);
    }
  };

  // 清除选择
  const clearSelection = () => {
    setSelectedStart(null);
    setSelectedEnd(null);
    setIsSelectingEnd(false);
    setIsOpen(false);
  };

  // 获取月份的所有日期
  const getMonthDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay()); // 从周日开始

    const days: Date[] = [];
    const current = new Date(startDate);
    
    // 生成6周的日期（42天）
    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    
    return days;
  };

  // 检查日期是否在选择范围内
  const isDateInRange = (date: Date): boolean => {
    if (!selectedStart || !selectedEnd) return false;
    return date >= selectedStart && date <= selectedEnd;
  };

  // 检查日期是否被选中
  const isDateSelected = (date: Date): boolean => {
    if (!selectedStart) return false;
    if (selectedEnd) {
      return date.getTime() === selectedStart.getTime() || date.getTime() === selectedEnd.getTime();
    }
    return date.getTime() === selectedStart.getTime();
  };

  // 检查日期是否在当前月
  const isCurrentMonth = (date: Date): boolean => {
    return date.getMonth() === currentMonth.getMonth() && date.getFullYear() === currentMonth.getFullYear();
  };

  // 导航到上个月
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  // 导航到下个月
  const goToNextMonth = () => {
    setCurrentMonth(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  // 快速选择预设范围
  const selectPresetRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - days + 1);
    
    const newRange: DateRange = {
      startDate: start,
      endDate: end,
      label: `Last ${days} days`
    };
    
    onChange(newRange);
    setIsOpen(false);
  };

  const monthDays = getMonthDays();
  const weekDays = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  return (
    <div className={`relative ${className}`}>
      {/* 触发按钮 */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className={value ? 'text-gray-900' : 'text-gray-500'}>
            {formatDisplayValue()}
          </span>
        </div>
        {value && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              clearSelection();
            }}
            className="ml-2 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </button>

      {/* 下拉面板 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 p-3 w-80">
          {/* 快速选择按钮 */}
          <div className="mb-3">
            <div className="text-xs font-medium text-gray-700 mb-2">Quick Select</div>
            <div className="flex flex-wrap gap-1">
              {[7, 14, 30, 90].map(days => (
                <button
                  key={days}
                  onClick={() => selectPresetRange(days)}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 rounded transition-colors"
                >
                  {days}d
                </button>
              ))}
            </div>
          </div>

          {/* 月份导航 */}
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={goToPreviousMonth}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <div className="text-sm font-medium">
              {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </div>
            <button
              onClick={goToNextMonth}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>

          {/* 日历网格 */}
          <div className="grid grid-cols-7 gap-1 mb-3">
            {/* 星期标题 */}
            {weekDays.map(day => (
              <div key={day} className="text-xs font-medium text-gray-500 text-center py-1">
                {day}
              </div>
            ))}
            
            {/* 日期网格 */}
            {monthDays.map((date, index) => {
              const isSelected = isDateSelected(date);
              const isInRange = isDateInRange(date);
              const isCurrentMonthDate = isCurrentMonth(date);
              const isToday = date.toDateString() === new Date().toDateString();
              
              return (
                <button
                  key={index}
                  onClick={() => handleDateClick(date)}
                  className={`
                    h-7 w-7 text-xs rounded transition-colors
                    ${isSelected 
                      ? 'bg-blue-600 text-white' 
                      : isInRange 
                      ? 'bg-blue-100 text-blue-800' 
                      : isCurrentMonthDate
                      ? 'hover:bg-gray-100 text-gray-900'
                      : 'text-gray-400 hover:bg-gray-50'
                    }
                    ${isToday && !isSelected ? 'border border-blue-600' : ''}
                  `}
                >
                  {date.getDate()}
                </button>
              );
            })}
          </div>

          {/* 状态提示和操作按钮 */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="text-xs text-gray-600">
              {!selectedStart ? 'Select start date' : 
               !selectedEnd ? 'Select end date' : 
               `${Math.ceil((selectedEnd.getTime() - selectedStart.getTime()) / (1000 * 60 * 60 * 24)) + 1} days selected`}
            </div>
            <div className="flex space-x-2">
              {(selectedStart || selectedEnd) && (
                <button
                  type="button"
                  onClick={clearSelection}
                  className="px-2 py-1 text-xs text-red-600 hover:text-red-800 border border-red-200 rounded hover:bg-red-50 transition-colors"
                >
                  Clear
                </button>
              )}
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompactDatePicker;
