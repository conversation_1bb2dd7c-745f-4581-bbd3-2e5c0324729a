# 统一媒体编辑器使用指南

## 概述

统一媒体编辑器是一个现代化的多媒体内容创作工具，将文本、音频、视频编辑功能整合到一个简约统一的界面中。该编辑器遵循项目代码生成规则，使用TypeScript强类型和Tailwind CSS，提供优秀的用户体验。

## 主要特性

### ✨ 核心功能

1. **统一界面设计**
   - 简约现代的UI设计
   - 三种媒体类型（文本、音频、视频）统一管理
   - 响应式布局，适配各种屏幕尺寸

2. **多媒体支持**
   - 📝 富文本编辑器（支持格式化、链接、列表等）
   - 🎵 音频文件上传和基础编辑
   - 🎬 视频文件上传和配置
   - 🖼️ 图片上传和设置

3. **实时预览**
   - 即时预览模式
   - 所见即所得编辑体验
   - 内容实时保存

## 界面布局

### 顶部工具栏
```
[Text] [Audio] [Video] | [Add Content] [Upload] | [Preview] [Save]
```

- **媒体类型选择**: 切换当前编辑的媒体类型
- **添加内容**: 快速添加新的内容块
- **上传文件**: 上传音频、视频、图片文件
- **预览模式**: 切换编辑/预览视图
- **保存**: 保存当前项目

### 左侧内容列表
- 显示所有内容项目
- 支持点击选择和删除
- 显示内容类型图标和基本信息
- 空状态提示

### 右侧编辑区域
- 根据选中的内容类型显示对应编辑器
- 文本编辑器：富文本编辑功能
- 音频编辑器：播放控制和元数据设置
- 视频编辑器：预览播放和配置选项
- 图片编辑器：预览和Alt文本设置

## 使用方法

### 1. 创建新项目

```typescript
// 访问统一编辑器页面
/content/unified-editor
```

### 2. 添加文本内容

1. 点击顶部工具栏的 "Add Text" 按钮
2. 在左侧列表中选择新创建的文本项目
3. 在右侧编辑区域开始写作
4. 使用文本格式化工具栏进行样式设置

**支持的文本格式：**
- **粗体** (Ctrl+B)
- *斜体* (Ctrl+I)
- <u>下划线</u> (Ctrl+U)
- 无序列表
- 有序列表
- 引用块
- 代码块
- 链接插入

### 3. 上传音频文件

1. 点击 "Upload" 按钮或切换到 Audio 标签页
2. 选择音频文件（支持常见格式：MP3, WAV, AAC等）
3. 配置音频元数据：
   - 标题
   - 描述
   - 分类标签

**音频编辑功能：**
- 播放控制
- 波形可视化（占位符）
- 元数据编辑
- 文件信息显示

### 4. 上传视频文件

1. 点击 "Upload" 按钮或切换到 Video 标签页
2. 选择视频文件（支持常见格式：MP4, WebM, AVI等）
3. 配置视频设置：
   - 标题和描述
   - 分类选择
   - 隐私设置（公开/私有/未列出）

**视频编辑功能：**
- 视频预览播放
- 缩略图显示
- 元数据编辑
- 隐私控制

### 5. 上传图片文件

1. 点击 "Upload" 按钮
2. 选择图片文件（支持：JPG, PNG, GIF, WebP等）
3. 设置图片属性：
   - Alt文本（用于无障碍访问）
   - 图片说明

## 项目设置

### 基本信息
- **标题**: 项目名称（必填）
- **描述**: 项目简介
- **分类**: 选择内容分类
- **标签**: 用逗号分隔的标签
- **可见性**: 公开/私有设置

### 项目统计
实时显示：
- 内容项目总数
- 文本块数量
- 媒体文件数量

## 技术特性

### 🔧 技术栈
- **前端框架**: React + Next.js 14
- **类型系统**: TypeScript（强类型，无any）
- **样式系统**: Tailwind CSS
- **状态管理**: React Hooks
- **文件处理**: 浏览器原生File API

### 📱 响应式设计
- 桌面端：完整功能布局
- 平板端：优化的触控体验
- 移动端：垂直堆叠布局

### ♿ 无障碍支持
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- Alt文本支持

## 数据结构

### MediaItem接口
```typescript
interface MediaItem {
  id: string;                    // 唯一标识符
  type: 'text' | 'audio' | 'video' | 'image';  // 媒体类型
  content: any;                  // 内容数据
  position: number;              // 排序位置
}
```

### 项目数据结构
```typescript
interface ProjectData {
  title: string;                 // 项目标题
  description: string;           // 项目描述
  content: MediaItem[];          // 内容项目数组
  category: string;              // 分类
  tags: string;                  // 标签
  isPublic: boolean;             // 是否公开
}
```

## 最佳实践

### 1. 内容组织
- 合理安排文本、音频、视频的顺序
- 使用描述性的标题和标签
- 保持内容结构清晰

### 2. 文件管理
- 使用适当的文件格式和大小
- 为媒体文件提供有意义的文件名
- 设置准确的Alt文本和描述

### 3. 性能优化
- 避免上传过大的媒体文件
- 使用压缩后的图片和视频
- 定期保存工作进度

### 4. 用户体验
- 利用预览模式检查最终效果
- 测试不同设备上的显示效果
- 确保内容的可访问性

## 快捷键

| 功能 | 快捷键 |
|------|--------|
| 粗体 | Ctrl+B |
| 斜体 | Ctrl+I |
| 下划线 | Ctrl+U |
| 保存 | Ctrl+S |
| 预览 | Ctrl+P |

## 浏览器兼容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过限制
   - 检查网络连接

2. **编辑器无响应**
   - 刷新页面重试
   - 清除浏览器缓存
   - 检查浏览器控制台错误

3. **保存失败**
   - 确保标题已填写
   - 检查网络连接
   - 重试保存操作

### 技术支持

如遇到技术问题，请：
1. 检查浏览器控制台错误信息
2. 确认浏览器版本兼容性
3. 联系技术支持团队

## 更新日志

### v1.0.0 (当前版本)
- ✨ 统一媒体编辑器首次发布
- 🎨 简约现代的UI设计
- 📝 富文本编辑功能
- 🎵 音频文件支持
- 🎬 视频文件支持
- 🖼️ 图片文件支持
- 📱 响应式设计
- ♿ 无障碍支持

---

**注意**: 这是一个演示版本，实际生产环境中需要集成真实的后端API和文件存储服务。
