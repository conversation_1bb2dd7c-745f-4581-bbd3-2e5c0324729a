'use client';

import React from 'react';

interface PerformanceData {
  time: string;
  cpu: number;
  memory: number;
  network: number;
}

interface SystemPerformanceChartProps {
  data: PerformanceData[];
  height?: number;
}

/**
 * 系统性能图表组件 - 专门用于显示CPU、内存、网络使用率
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const SystemPerformanceChart: React.FC<SystemPerformanceChartProps> = ({ data, height = 320 }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-gray-500">No performance data available</p>
        </div>
      </div>
    );
  }

  // 性能数据的最大值是100%
  const maxValue = 100;

  // 格式化百分比
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  // 格式化时间
  const formatTime = (timeStr: string): string => {
    try {
      // 如果是时间格式 (HH:MM)
      if (timeStr && timeStr.includes(':')) {
        return timeStr;
      }
      // 如果是其他格式，尝试解析
      const date = new Date(timeStr);
      if (!isNaN(date.getTime())) {
        return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
      }
      return timeStr || 'N/A';
    } catch {
      return timeStr || 'N/A';
    }
  };

  return (
    <div className="w-full" style={{ height }}>
      {/* 图例 */}
      <div className="flex justify-center space-x-6 mb-4">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">CPU Usage</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Memory Usage</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
          <span className="text-sm text-gray-600">Network Usage</span>
        </div>
      </div>

      {/* 图表区域 - 使用更直观的面积图 */}
      <div className="relative bg-gradient-to-b from-gray-50 to-white border border-gray-200 rounded-lg p-4" style={{ height: height - 60 }}>
        {/* Y轴标签 */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-2">
          <span>100%</span>
          <span>75%</span>
          <span>50%</span>
          <span>25%</span>
          <span>0%</span>
        </div>

        {/* 图表内容 */}
        <div className="ml-12 h-full relative overflow-hidden">
          {/* 网格线 */}
          <div className="absolute inset-0">
            {[0, 25, 50, 75, 100].map((percent) => (
              <div
                key={percent}
                className="absolute w-full border-t border-gray-100"
                style={{ top: `${100 - percent}%` }}
              />
            ))}
          </div>

          {/* 面积图背景 */}
          <svg className="absolute inset-0 w-full h-full" preserveAspectRatio="none">
            <defs>
              <linearGradient id="cpuGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3"/>
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.1"/>
              </linearGradient>
              <linearGradient id="memoryGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#10B981" stopOpacity="0.3"/>
                <stop offset="100%" stopColor="#10B981" stopOpacity="0.1"/>
              </linearGradient>
              <linearGradient id="networkGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#F59E0B" stopOpacity="0.3"/>
                <stop offset="100%" stopColor="#F59E0B" stopOpacity="0.1"/>
              </linearGradient>
            </defs>

            {/* CPU面积图 */}
            <path
              d={`M 0 100% ${data.map((item, index) =>
                `L ${(index / (data.length - 1)) * 100}% ${100 - item.cpu}%`
              ).join(' ')} L 100% 100% Z`}
              fill="url(#cpuGradient)"
            />

            {/* Memory面积图 */}
            <path
              d={`M 0 100% ${data.map((item, index) =>
                `L ${(index / (data.length - 1)) * 100}% ${100 - item.memory}%`
              ).join(' ')} L 100% 100% Z`}
              fill="url(#memoryGradient)"
            />

            {/* Network面积图 */}
            <path
              d={`M 0 100% ${data.map((item, index) =>
                `L ${(index / (data.length - 1)) * 100}% ${100 - item.network}%`
              ).join(' ')} L 100% 100% Z`}
              fill="url(#networkGradient)"
            />

            {/* CPU线条 */}
            <path
              d={`M ${data.map((item, index) =>
                `${index === 0 ? 'M' : 'L'} ${(index / (data.length - 1)) * 100}% ${100 - item.cpu}%`
              ).join(' ')}`}
              fill="none"
              stroke="#3B82F6"
              strokeWidth="2"
            />

            {/* Memory线条 */}
            <path
              d={`M ${data.map((item, index) =>
                `${index === 0 ? 'M' : 'L'} ${(index / (data.length - 1)) * 100}% ${100 - item.memory}%`
              ).join(' ')}`}
              fill="none"
              stroke="#10B981"
              strokeWidth="2"
            />

            {/* Network线条 */}
            <path
              d={`M ${data.map((item, index) =>
                `${index === 0 ? 'M' : 'L'} ${(index / (data.length - 1)) * 100}% ${100 - item.network}%`
              ).join(' ')}`}
              fill="none"
              stroke="#F59E0B"
              strokeWidth="2"
            />
          </svg>

          {/* 数据点和线条 */}
          <div className="relative h-full flex items-end justify-between">
            {data.map((item, index) => {
              // 使用固定的100%作为最大值，因为数据已经是百分比
              const cpuHeight = item.cpu;
              const memoryHeight = item.memory;
              const networkHeight = item.network;

              return (
                <div key={index} className="flex flex-col items-center group relative">
                  {/* 数据点 */}
                  <div className="relative mb-2" style={{ height: '200px' }}>
                    {/* CPU点 */}
                    <div
                      className="absolute w-2 h-2 bg-blue-500 rounded-full transform -translate-x-1/2 hover:w-3 hover:h-3 transition-all duration-200"
                      style={{ bottom: `${cpuHeight}%`, left: '50%' }}
                    />
                    {/* Memory点 */}
                    <div
                      className="absolute w-2 h-2 bg-green-500 rounded-full transform -translate-x-1/2 hover:w-3 hover:h-3 transition-all duration-200"
                      style={{ bottom: `${memoryHeight}%`, left: '50%' }}
                    />
                    {/* Network点 */}
                    <div
                      className="absolute w-2 h-2 bg-orange-500 rounded-full transform -translate-x-1/2 hover:w-3 hover:h-3 transition-all duration-200"
                      style={{ bottom: `${networkHeight}%`, left: '50%' }}
                    />
                  </div>

                  {/* X轴标签 */}
                  <div className="text-xs text-gray-500 text-center">
                    {formatTime(item.time)}
                  </div>

                  {/* 悬停提示 */}
                  <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    <div>CPU: {formatPercentage(item.cpu)}</div>
                    <div>Memory: {formatPercentage(item.memory)}</div>
                    <div>Network: {formatPercentage(item.network)}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemPerformanceChart;
