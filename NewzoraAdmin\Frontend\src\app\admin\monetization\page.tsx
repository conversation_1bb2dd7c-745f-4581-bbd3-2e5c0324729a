'use client';

import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, Users, Eye, CheckCircle, Clock, CreditCard, ArrowUpRight, ArrowDownRight, BarChart3, PieChart } from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar, PieChart as RechartsPieChart, Cell } from 'recharts';

/**
 * 收益概览页面 - 收益总览和快速导航
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */


const MonetizationPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('area');
  const [stats, setStats] = useState({
    totalRevenue: 125600,
    totalWithdrawals: 45200,
    pendingWithdrawals: 8900,
    completedWithdrawals: 36300,
    adImpressions: 245000,
    averageCTR: 3.2,
    premiumUsers: 1240
  });

  // 收入趋势数据 - 扩展为30天数据
  const [revenueTrendData, setRevenueTrendData] = useState(() => {
    const data = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 29); // 30天前

    for (let i = 0; i < 30; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      // 生成模拟数据，带有一些趋势和随机性
      const baseRevenue = 8000 + Math.sin(i / 5) * 2000 + Math.random() * 3000;
      const baseWithdrawals = baseRevenue * (0.2 + Math.random() * 0.1);
      const adRevenue = baseRevenue * (0.4 + Math.random() * 0.2);
      const subscriptionRevenue = baseRevenue * (0.3 + Math.random() * 0.1);
      const otherRevenue = baseRevenue - adRevenue - subscriptionRevenue;

      data.push({
        date: currentDate.toISOString().split('T')[0],
        name: currentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        revenue: Math.round(baseRevenue),
        withdrawals: Math.round(baseWithdrawals),
        netRevenue: Math.round(baseRevenue - baseWithdrawals),
        adRevenue: Math.round(adRevenue),
        subscriptionRevenue: Math.round(subscriptionRevenue),
        otherRevenue: Math.round(otherRevenue),
        users: Math.round(50 + Math.random() * 100),
        conversions: Math.round(5 + Math.random() * 15)
      });
    }

    return data;
  });

  // 收入来源分布数据
  const [revenueSourceData, setRevenueSourceData] = useState([
    { name: 'Ad Revenue', value: 45000, color: '#3B82F6' },
    { name: 'Subscriptions', value: 35000, color: '#10B981' },
    { name: 'Premium Features', value: 25000, color: '#F59E0B' },
    { name: 'Partnerships', value: 15000, color: '#EF4444' },
    { name: 'Other', value: 5600, color: '#8B5CF6' }
  ]);

  // Generate comprehensive withdrawal records for testing
  const generateWithdrawalRecords = () => {
    const records = [];
    const users = [
      { username: 'john_doe', email: '<EMAIL>', userId: 'user123' },
      { username: 'jane_smith', email: '<EMAIL>', userId: 'user456' },
      { username: 'mike_wilson', email: '<EMAIL>', userId: 'user789' },
      { username: 'sarah_jones', email: '<EMAIL>', userId: 'user321' },
      { username: 'david_brown', email: '<EMAIL>', userId: 'user654' },
      { username: 'lisa_garcia', email: '<EMAIL>', userId: 'user987' },
      { username: 'tom_wilson', email: '<EMAIL>', userId: 'user246' },
      { username: 'emma_davis', email: '<EMAIL>', userId: 'user135' }
    ];
    const methods = ['PayPal', 'Bank Transfer', 'Cryptocurrency', 'Stripe'];
    const statuses = ['pending', 'approved', 'completed', 'rejected', 'processing'];

    // Add some predefined records first
    const predefinedRecords = [
      {
        id: 'WD001',
        userId: 'user123',
        username: 'john_doe',
        email: '<EMAIL>',
        amount: 500.00,
        method: 'PayPal',
        accountInfo: '<EMAIL>',
        status: 'pending',
        requestDate: '2024-01-15T10:30:00Z',
        processedDate: null,
        notes: '',
        adminNotes: '',
        user: 'John Doe',
        date: '2024-01-15',
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 'WD002',
        userId: 'user456',
        username: 'jane_smith',
        email: '<EMAIL>',
        amount: 750.00,
        method: 'Bank Transfer',
        accountInfo: 'Bank: Chase, Account: ****1234',
        status: 'approved',
        requestDate: '2024-01-14T14:20:00Z',
        processedDate: '2024-01-15T09:15:00Z',
        notes: 'Urgent withdrawal request',
        adminNotes: 'Verified account details',
        user: 'Jane Smith',
        date: '2024-01-14',
        createdAt: '2024-01-14T14:20:00Z'
      }
    ];

    records.push(...predefinedRecords);

    // Generate additional random records
    for (let i = 3; i <= 50; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365));
      const userIndex = Math.floor(Math.random() * users.length);
      const user = users[userIndex];
      const methodIndex = Math.floor(Math.random() * methods.length);
      const method = methods[methodIndex];
      const statusIndex = Math.floor(Math.random() * statuses.length);
      const status = statuses[statusIndex];

      // 确保user不为undefined
      if (!user || !method || !status) continue;

      records.push({
        id: `WD${i.toString().padStart(3, '0')}`,
        userId: user.userId,
        username: user.username,
        email: user.email,
        amount: Math.floor(Math.random() * 1000) + 100,
        method: method,
        accountInfo: method === 'PayPal' ? `${user.username}@paypal.com` :
                    method === 'Bank Transfer' ? `Bank: Chase, Account: ****${Math.floor(Math.random() * 9999)}` :
                    method === 'Cryptocurrency' ? `BTC: 1${Math.random().toString(36).substring(2, 15)}` :
                    `Stripe: ****${Math.floor(Math.random() * 9999)}`,
        status: status,
        requestDate: date.toISOString(),
        processedDate: status === 'completed' || status === 'rejected' ?
                      new Date(date.getTime() + Math.random() * ********).toISOString() : null,
        notes: Math.random() > 0.7 ? 'Regular withdrawal request' : '',
        adminNotes: status === 'completed' ? 'Processed successfully' :
                   status === 'rejected' ? 'Insufficient verification' : '',
        user: user.username.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        date: date.toISOString().split('T')[0],
        createdAt: date.toISOString()
      });
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };
  

  
  // Ad placement data
  const adPlacements = [
    { id: '1', name: 'Homepage Banner', type: 'Banner Ad', status: 'Active', revenue: 3200, impressions: 64000, ctr: 3.2 },
    { id: '2', name: 'Article Inline', type: 'Content Ad', status: 'Active', revenue: 2800, impressions: 56000, ctr: 2.8 },
    { id: '3', name: 'Sidebar', type: 'Display Ad', status: 'Active', revenue: 1500, impressions: 30000, ctr: 2.5 },
    { id: '4', name: 'Popup Ad', type: 'Interstitial', status: 'Paused', revenue: 800, impressions: 12000, ctr: 4.2 },
  ];
  
  // 提现记录数据状态
  const [withdrawalRecords, setWithdrawalRecords] = useState(() => generateWithdrawalRecords());
  const [allWithdrawalRecords, setAllWithdrawalRecords] = useState(() => generateWithdrawalRecords());

  useEffect(() => {
    // 初始化数据
    const allRecords = generateWithdrawalRecords();
    setAllWithdrawalRecords(allRecords);
    setWithdrawalRecords(allRecords.slice(0, 10)); // 显示前10条

    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // 根据日期范围过滤提现记录
    if (allWithdrawalRecords.length > 0) {
      // 简单的日期过滤逻辑
      const filteredRecords = allWithdrawalRecords.filter(record => {
        const recordDate = new Date(record.createdAt);
        return recordDate >= dateRange.startDate && recordDate <= dateRange.endDate;
      });
      setWithdrawalRecords(filteredRecords.slice(0, 10)); // 显示前10条
    }

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    setStats({
      totalRevenue: Math.floor(125600 * multiplier),
      totalWithdrawals: Math.floor(45200 * multiplier),
      pendingWithdrawals: Math.floor(8900 * multiplier),
      completedWithdrawals: Math.floor(36300 * multiplier),
      adImpressions: Math.floor(245000 * multiplier),
      averageCTR: 3.2 + (Math.random() - 0.5) * 0.5, // 小幅波动
      premiumUsers: Math.floor(1240 * multiplier)
    });

    // 更新图表数据 - 根据天数范围决定数据粒度
    const formatDate = (date: Date): string => {
      return date.toISOString().split('T')[0] || date.toLocaleDateString('en-CA');
    };

    const startDateStr = formatDate(dateRange.startDate);
    const endDateStr = formatDate(dateRange.endDate);

    if (daysDiff <= 7) {
      // 7天内显示每日数据

      // 更新收入趋势数据

      const dailyData = [];
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(dateRange.startDate.getTime() + i * ********);
        const baseRevenue = Math.max(0, Math.floor((8500 + Math.random() * 4000) * multiplier));
        const baseWithdrawals = Math.max(0, Math.floor(baseRevenue * 0.25));
        const adRevenue = Math.floor(baseRevenue * 0.4);
        const subscriptionRevenue = Math.floor(baseRevenue * 0.35);
        const otherRevenue = baseRevenue - adRevenue - subscriptionRevenue;

        dailyData.push({
          date: formatDate(currentDate),
          name: currentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          revenue: baseRevenue,
          withdrawals: baseWithdrawals,
          netRevenue: baseRevenue - baseWithdrawals,
          adRevenue,
          subscriptionRevenue,
          otherRevenue,
          users: Math.round(50 + Math.random() * 50),
          conversions: Math.round(5 + Math.random() * 10)
        });
      }
      setRevenueTrendData(dailyData);
    } else if (daysDiff <= 30) {
      // 30天内显示每周数据

      // 更新收入趋势数据 - 每周数据
      const weeklyData = [];
      for (let i = 1; i <= 4; i++) {
        const baseRevenue = Math.floor((35000 + Math.random() * 7000) * multiplier);
        const baseWithdrawals = Math.floor(baseRevenue * 0.25);
        const adRevenue = Math.floor(baseRevenue * 0.4);
        const subscriptionRevenue = Math.floor(baseRevenue * 0.35);
        const otherRevenue = baseRevenue - adRevenue - subscriptionRevenue;

        weeklyData.push({
          date: `Week ${i}`,
          name: `Week ${i}`,
          revenue: baseRevenue,
          withdrawals: baseWithdrawals,
          netRevenue: baseRevenue - baseWithdrawals,
          adRevenue,
          subscriptionRevenue,
          otherRevenue,
          users: Math.round(200 + Math.random() * 100),
          conversions: Math.round(20 + Math.random() * 30)
        });
      }
      setRevenueTrendData(weeklyData);
    } else {
      // 超过30天显示每月数据

      // 更新收入趋势数据 - 每月数据
      const monthlyData = [];
      for (let i = 1; i <= 3; i++) {
        const baseRevenue = Math.floor((150000 + i * 30000 + Math.random() * 20000) * multiplier);
        const baseWithdrawals = Math.floor(baseRevenue * 0.25);
        const adRevenue = Math.floor(baseRevenue * 0.4);
        const subscriptionRevenue = Math.floor(baseRevenue * 0.35);
        const otherRevenue = baseRevenue - adRevenue - subscriptionRevenue;

        monthlyData.push({
          date: `Month ${i}`,
          name: `Month ${i}`,
          revenue: baseRevenue,
          withdrawals: baseWithdrawals,
          netRevenue: baseRevenue - baseWithdrawals,
          adRevenue,
          subscriptionRevenue,
          otherRevenue,
          users: Math.round(800 + i * 200 + Math.random() * 300),
          conversions: Math.round(80 + i * 20 + Math.random() * 50)
        });
      }
      setRevenueTrendData(monthlyData);
    }
  };

  // 处理函数已移至专门的提现管理页面 (/admin/withdrawals)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Revenue Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              💰 Revenue Overview
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive revenue tracking and financial analytics dashboard
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <CompactDatePicker
              value={currentDateRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range"
              className="w-full sm:w-56"
            />
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-3xl font-bold text-green-600">${stats.totalRevenue.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600 font-medium">+12%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Withdrawals</p>
              <p className="text-3xl font-bold text-yellow-600">${stats.pendingWithdrawals.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowDownRight className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-sm text-yellow-600 font-medium">-5%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Withdrawals</p>
              <p className="text-3xl font-bold text-blue-600">${stats.completedWithdrawals.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-sm text-blue-600 font-medium">+8%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Premium Users</p>
              <p className="text-3xl font-bold text-purple-600">{stats.premiumUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-purple-500 mr-1" />
                <span className="text-sm text-purple-600 font-medium">+15%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 快速导航卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Advertising Management</h3>
                <p className="text-gray-600">Manage ad placements and revenue</p>
              </div>
            </div>
            <a
              href="/admin/advertising"
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Manage Ads
            </a>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Active Ads</p>
              <p className="font-bold text-lg">{adPlacements.filter(ad => ad.status === 'Active').length}</p>
            </div>
            <div>
              <p className="text-gray-500">Ad Revenue</p>
              <p className="font-bold text-lg text-green-600">${adPlacements.reduce((sum, ad) => sum + ad.revenue, 0).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-gray-500">Avg CTR</p>
              <p className="font-bold text-lg">{(adPlacements.reduce((sum, ad) => sum + ad.ctr, 0) / adPlacements.length).toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">Withdrawal Management</h3>
                <p className="text-gray-600">Process user withdrawals</p>
              </div>
            </div>
            <a
              href="/admin/withdrawals"
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Manage Withdrawals
            </a>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-gray-500">Pending</p>
              <p className="font-bold text-lg text-yellow-600">{withdrawalRecords.filter(r => r.status === 'pending').length}</p>
            </div>
            <div>
              <p className="text-gray-500">Completed</p>
              <p className="font-bold text-lg text-green-600">{withdrawalRecords.filter(r => r.status === 'completed').length}</p>
            </div>
            <div>
              <p className="text-gray-500">Total Amount</p>
              <p className="font-bold text-lg">${withdrawalRecords.reduce((sum, r) => sum + r.amount, 0).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Trend Chart */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mt-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Revenue Trend Analysis</h3>
              <p className="text-gray-600">Track revenue performance over time</p>
            </div>
          </div>
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Total Revenue</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-red-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Withdrawals</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Net Revenue</span>
            </div>
          </div>
        </div>

        {/* 图表类型选择器 */}
        <div className="flex items-center space-x-4 mb-6">
          <span className="text-sm font-medium text-gray-700">Chart Type:</span>
          <div className="flex space-x-2">
            {[
              { type: 'area', label: 'Area Chart', icon: BarChart3 },
              { type: 'line', label: 'Line Chart', icon: TrendingUp },
              { type: 'bar', label: 'Bar Chart', icon: BarChart3 }
            ].map(({ type, label, icon: Icon }) => (
              <button
                key={type}
                onClick={() => setChartType(type as any)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  chartType === type
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="h-80">
          {chartType === 'area' && (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={revenueTrendData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip
                  formatter={(value: any, name: string) => [
                    `$${Number(value).toLocaleString()}`,
                    name === 'revenue' ? 'Total Revenue' :
                    name === 'netRevenue' ? 'Net Revenue' :
                    name === 'withdrawals' ? 'Withdrawals' : name
                  ]}
                  labelFormatter={(label) => `Period: ${label}`}
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stackId="1"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="withdrawals"
                  stackId="2"
                  stroke="#ef4444"
                  fill="#ef4444"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="netRevenue"
                  stackId="3"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}

          {chartType === 'line' && (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={revenueTrendData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip
                  formatter={(value: any, name: string) => [
                    `$${Number(value).toLocaleString()}`,
                    name === 'revenue' ? 'Total Revenue' :
                    name === 'netRevenue' ? 'Net Revenue' :
                    name === 'withdrawals' ? 'Withdrawals' : name
                  ]}
                  labelFormatter={(label) => `Period: ${label}`}
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="withdrawals"
                  stroke="#ef4444"
                  strokeWidth={3}
                  dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="netRevenue"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}

          {chartType === 'bar' && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={revenueTrendData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis
                  stroke="#6b7280"
                  fontSize={12}
                  tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                />
                <Tooltip
                  formatter={(value: any, name: string) => [
                    `$${Number(value).toLocaleString()}`,
                    name === 'revenue' ? 'Total Revenue' :
                    name === 'netRevenue' ? 'Net Revenue' :
                    name === 'withdrawals' ? 'Withdrawals' : name
                  ]}
                  labelFormatter={(label) => `Period: ${label}`}
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar dataKey="revenue" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                <Bar dataKey="withdrawals" fill="#ef4444" radius={[4, 4, 0, 0]} />
                <Bar dataKey="netRevenue" fill="#10b981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total Revenue</p>
                <p className="text-lg font-semibold text-blue-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-900">Total Withdrawals</p>
                <p className="text-lg font-semibold text-red-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.withdrawals, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">Net Revenue</p>
                <p className="text-lg font-semibold text-green-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.netRevenue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonetizationPage;