'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Type,
  Music,
  Video,
  Image,
  Bold,
  Italic,
  Underline,
  Link,
  List,
  ListOrdered,
  Quote,
  Code,
  Save,
  Eye,
  Upload,
  Play,
  Pause,
  Volume2,
  Settings,
  X,
  Plus,
  Trash2,
  Edit3
} from 'lucide-react';

/**
 * 统一多媒体编辑器 - 整合文本、音频、视频编辑功能
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、简约设计
 */

interface MediaItem {
  id: string;
  type: 'text' | 'audio' | 'video' | 'image';
  content: any;
  position: number;
}

interface UnifiedMediaEditorProps {
  initialContent?: MediaItem[];
  onChange?: (content: MediaItem[]) => void;
  onSave?: (content: MediaItem[]) => void;
  className?: string;
}

const UnifiedMediaEditor: React.FC<UnifiedMediaEditorProps> = ({
  initialContent = [],
  onChange,
  onSave,
  className = ''
}) => {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>(initialContent);
  const [activeTab, setActiveTab] = useState<'text' | 'audio' | 'video'>('text');
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  
  const textEditorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 添加新的媒体项目
  const addMediaItem = (type: 'text' | 'audio' | 'video' | 'image') => {
    const newItem: MediaItem = {
      id: `${type}_${Date.now()}`,
      type,
      content: type === 'text' ? '' : null,
      position: mediaItems.length
    };
    
    const updatedItems = [...mediaItems, newItem];
    setMediaItems(updatedItems);
    setSelectedItem(newItem.id);
    onChange?.(updatedItems);
  };

  // 删除媒体项目
  const removeMediaItem = (id: string) => {
    const updatedItems = mediaItems.filter(item => item.id !== id);
    setMediaItems(updatedItems);
    if (selectedItem === id) {
      setSelectedItem(null);
    }
    onChange?.(updatedItems);
  };

  // 更新媒体项目内容
  const updateMediaItem = (id: string, content: any) => {
    const updatedItems = mediaItems.map(item =>
      item.id === id ? { ...item, content } : item
    );
    setMediaItems(updatedItems);
    onChange?.(updatedItems);
  };

  // 文本格式化功能
  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (textEditorRef.current && selectedItem) {
      updateMediaItem(selectedItem, textEditorRef.current.innerHTML);
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileType = file.type.startsWith('audio/') ? 'audio' : 
                    file.type.startsWith('video/') ? 'video' : 'image';
    
    // 模拟文件上传
    const fileUrl = URL.createObjectURL(file);
    const newItem: MediaItem = {
      id: `${fileType}_${Date.now()}`,
      type: fileType as 'audio' | 'video' | 'image',
      content: {
        url: fileUrl,
        name: file.name,
        size: file.size,
        type: file.type
      },
      position: mediaItems.length
    };

    const updatedItems = [...mediaItems, newItem];
    setMediaItems(updatedItems);
    setSelectedItem(newItem.id);
    onChange?.(updatedItems);
  };

  const selectedMediaItem = mediaItems.find(item => item.id === selectedItem);

  return (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-200 ${className}`}>
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-1">
          {/* 媒体类型选择 */}
          <div className="flex items-center bg-white rounded-lg p-1 shadow-sm">
            {[
              { type: 'text', icon: Type, label: 'Text' },
              { type: 'audio', icon: Music, label: 'Audio' },
              { type: 'video', icon: Video, label: 'Video' }
            ].map(({ type, icon: Icon, label }) => (
              <button
                key={type}
                onClick={() => setActiveTab(type as any)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === type
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>

          {/* 分隔线 */}
          <div className="h-6 w-px bg-gray-300 mx-3" />

          {/* 添加内容按钮 */}
          <button
            onClick={() => addMediaItem(activeTab)}
            className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            <Plus className="w-4 h-4" />
            <span>Add {activeTab}</span>
          </button>

          {/* 上传文件按钮 */}
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm font-medium"
          >
            <Upload className="w-4 h-4" />
            <span>Upload</span>
          </button>
        </div>

        {/* 右侧操作按钮 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              isPreviewMode
                ? 'bg-purple-100 text-purple-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <Eye className="w-4 h-4" />
            <span>Preview</span>
          </button>

          <button
            onClick={() => onSave?.(mediaItems)}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium"
          >
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>
        </div>
      </div>

      {/* 文本编辑工具栏 */}
      {activeTab === 'text' && selectedMediaItem?.type === 'text' && !isPreviewMode && (
        <div className="flex items-center space-x-1 p-3 border-b border-gray-200 bg-gray-50">
          {[
            { command: 'bold', icon: Bold, title: 'Bold' },
            { command: 'italic', icon: Italic, title: 'Italic' },
            { command: 'underline', icon: Underline, title: 'Underline' },
            { command: 'insertUnorderedList', icon: List, title: 'Bullet List' },
            { command: 'insertOrderedList', icon: ListOrdered, title: 'Numbered List' },
            { command: 'formatBlock', icon: Quote, title: 'Quote', value: 'blockquote' },
            { command: 'formatBlock', icon: Code, title: 'Code Block', value: 'pre' }
          ].map(({ command, icon: Icon, title, value }) => (
            <button
              key={command}
              onClick={() => formatText(command, value)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
              title={title}
            >
              <Icon className="w-4 h-4" />
            </button>
          ))}
          
          <div className="h-6 w-px bg-gray-300 mx-2" />
          
          <button
            onClick={() => {
              const url = prompt('Enter URL:');
              if (url) formatText('createLink', url);
            }}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
            title="Insert Link"
          >
            <Link className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* 主编辑区域 */}
      <div className="flex min-h-[500px]">
        {/* 左侧内容列表 */}
        <div className="w-64 border-r border-gray-200 bg-gray-50">
          <div className="p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Content Items</h3>
            <div className="space-y-2">
              {mediaItems.map((item, index) => (
                <div
                  key={item.id}
                  onClick={() => setSelectedItem(item.id)}
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedItem === item.id
                      ? 'bg-blue-100 border border-blue-200'
                      : 'bg-white hover:bg-gray-100 border border-gray-200'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {item.type === 'text' && <Type className="w-4 h-4 text-gray-600" />}
                    {item.type === 'audio' && <Music className="w-4 h-4 text-orange-600" />}
                    {item.type === 'video' && <Video className="w-4 h-4 text-blue-600" />}
                    {item.type === 'image' && <Image className="w-4 h-4 text-green-600" />}
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)} {index + 1}
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.type === 'text' ? 'Text content' : item.content?.name || 'Media file'}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeMediaItem(item.id);
                    }}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
              
              {mediaItems.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Edit3 className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">No content yet</p>
                  <p className="text-xs">Add text, audio, or video</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧编辑区域 */}
        <div className="flex-1 flex flex-col">
          {selectedMediaItem ? (
            <div className="flex-1 p-6">
              {/* 文本编辑器 */}
              {selectedMediaItem.type === 'text' && (
                <div className="h-full">
                  {isPreviewMode ? (
                    <div
                      className="prose prose-lg max-w-none h-full overflow-y-auto p-4 bg-gray-50 rounded-lg"
                      dangerouslySetInnerHTML={{ __html: selectedMediaItem.content || '<p class="text-gray-500">No content yet...</p>' }}
                    />
                  ) : (
                    <div
                      ref={textEditorRef}
                      contentEditable
                      onInput={(e) => updateMediaItem(selectedMediaItem.id, e.currentTarget.innerHTML)}
                      className="w-full h-full min-h-[400px] p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      style={{
                        fontSize: '16px',
                        lineHeight: '1.6',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                      }}
                      dangerouslySetInnerHTML={{ __html: selectedMediaItem.content || '' }}
                      data-placeholder="Start writing your content..."
                    />
                  )}
                </div>
              )}

              {/* 音频编辑器 */}
              {selectedMediaItem.type === 'audio' && (
                <div className="h-full flex flex-col">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Audio Editor</h3>
                    <p className="text-sm text-gray-600">Configure your audio content</p>
                  </div>

                  {selectedMediaItem.content ? (
                    <div className="space-y-6">
                      {/* 音频播放器 */}
                      <div className="bg-gray-50 rounded-lg p-6">
                        <div className="flex items-center space-x-4 mb-4">
                          <button className="p-3 bg-orange-600 text-white rounded-full hover:bg-orange-700 transition-colors">
                            <Play className="w-6 h-6" />
                          </button>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-900">{selectedMediaItem.content.name}</div>
                            <div className="text-xs text-gray-500">
                              {(selectedMediaItem.content.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                          </div>
                          <Volume2 className="w-5 h-5 text-gray-600" />
                        </div>

                        {/* 音频波形占位符 */}
                        <div className="h-20 bg-white rounded border border-gray-200 flex items-center justify-center">
                          <div className="text-sm text-gray-500">Audio waveform visualization</div>
                        </div>
                      </div>

                      {/* 音频设置 */}
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                            placeholder="Enter audio title..."
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                          <textarea
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                            rows={3}
                            placeholder="Enter audio description..."
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <Music className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4">No audio file selected</p>
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
                        >
                          Upload Audio
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 视频编辑器 */}
              {selectedMediaItem.type === 'video' && (
                <div className="h-full flex flex-col">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Video Editor</h3>
                    <p className="text-sm text-gray-600">Configure your video content</p>
                  </div>

                  {selectedMediaItem.content ? (
                    <div className="space-y-6">
                      {/* 视频预览 */}
                      <div className="bg-gray-50 rounded-lg p-6">
                        <div className="aspect-video bg-black rounded-lg mb-4 flex items-center justify-center">
                          <video
                            src={selectedMediaItem.content.url}
                            className="w-full h-full rounded-lg"
                            controls
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{selectedMediaItem.content.name}</div>
                            <div className="text-xs text-gray-500">
                              {(selectedMediaItem.content.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                          </div>
                          <button className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                            <Settings className="w-4 h-4" />
                            <span>Settings</span>
                          </button>
                        </div>
                      </div>

                      {/* 视频设置 */}
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter video title..."
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                          <textarea
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            rows={3}
                            placeholder="Enter video description..."
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                              <option value="">Select category...</option>
                              <option value="education">Education</option>
                              <option value="entertainment">Entertainment</option>
                              <option value="technology">Technology</option>
                              <option value="lifestyle">Lifestyle</option>
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">Privacy</label>
                            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                              <option value="public">Public</option>
                              <option value="private">Private</option>
                              <option value="unlisted">Unlisted</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <Video className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4">No video file selected</p>
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          Upload Video
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 图片编辑器 */}
              {selectedMediaItem.type === 'image' && (
                <div className="h-full flex flex-col">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Image Editor</h3>
                    <p className="text-sm text-gray-600">Configure your image content</p>
                  </div>

                  {selectedMediaItem.content ? (
                    <div className="space-y-6">
                      {/* 图片预览 */}
                      <div className="bg-gray-50 rounded-lg p-6">
                        <img
                          src={selectedMediaItem.content.url}
                          alt="Preview"
                          className="max-w-full max-h-64 mx-auto rounded-lg shadow-sm"
                        />
                        <div className="mt-4 text-center">
                          <div className="text-sm font-medium text-gray-900">{selectedMediaItem.content.name}</div>
                          <div className="text-xs text-gray-500">
                            {(selectedMediaItem.content.size / 1024 / 1024).toFixed(2)} MB
                          </div>
                        </div>
                      </div>

                      {/* 图片设置 */}
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Alt Text</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                            placeholder="Enter image description..."
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Caption</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                            placeholder="Enter image caption..."
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <Image className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 mb-4">No image file selected</p>
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                        >
                          Upload Image
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Edit3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select Content to Edit</h3>
                <p className="text-gray-500 mb-6">Choose an item from the left panel or create new content</p>
                <div className="flex space-x-3 justify-center">
                  <button
                    onClick={() => addMediaItem('text')}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <Type className="w-4 h-4" />
                    <span>Add Text</span>
                  </button>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Upload Media</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*,video/*,image/*"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  );
};

export default UnifiedMediaEditor;
