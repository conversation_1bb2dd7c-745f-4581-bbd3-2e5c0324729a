/* 统一媒体编辑器样式 */

/* 文本编辑器样式 */
.unified-editor [contenteditable] {
  outline: none;
}

.unified-editor [contenteditable]:empty:before {
  content: attr(data-placeholder);
  color: #9CA3AF;
  pointer-events: none;
  position: absolute;
}

.unified-editor [contenteditable]:focus:before {
  display: none;
}

/* 工具栏按钮悬停效果 */
.unified-editor .toolbar-button {
  transition: all 0.2s ease-in-out;
}

.unified-editor .toolbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 媒体项目卡片样式 */
.unified-editor .media-item-card {
  transition: all 0.2s ease-in-out;
}

.unified-editor .media-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.unified-editor .media-item-card.selected {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 音频波形动画 */
.unified-editor .audio-waveform {
  background: linear-gradient(90deg, #F59E0B 0%, #F59E0B 50%, #FCD34D 50%, #FCD34D 100%);
  background-size: 20px 100%;
  animation: waveform 2s ease-in-out infinite;
}

@keyframes waveform {
  0%, 100% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 0%;
  }
}

/* 视频播放器样式 */
.unified-editor .video-player {
  position: relative;
  overflow: hidden;
}

.unified-editor .video-player:hover .video-controls {
  opacity: 1;
}

.unified-editor .video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  opacity: 0;
  transition: opacity 0.3s ease;
  padding: 1rem;
}

/* 拖拽排序样式 */
.unified-editor .sortable-item {
  transition: transform 0.2s ease;
}

.unified-editor .sortable-item.dragging {
  transform: rotate(5deg);
  opacity: 0.8;
  z-index: 1000;
}

.unified-editor .drop-zone {
  border: 2px dashed #D1D5DB;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.2s ease;
}

.unified-editor .drop-zone.active {
  border-color: #3B82F6;
  background-color: rgba(59, 130, 246, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .unified-editor .toolbar {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .unified-editor .media-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #E5E7EB;
  }
  
  .unified-editor .editor-layout {
    flex-direction: column;
  }
}

/* 加载动画 */
.unified-editor .loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 成功/错误状态 */
.unified-editor .status-success {
  background-color: #10B981;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  animation: slideIn 0.3s ease;
}

.unified-editor .status-error {
  background-color: #EF4444;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 文本编辑器内容样式 */
.unified-editor .prose {
  max-width: none;
}

.unified-editor .prose h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.unified-editor .prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #111827;
}

.unified-editor .prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111827;
}

.unified-editor .prose p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: #374151;
}

.unified-editor .prose ul,
.unified-editor .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.unified-editor .prose li {
  margin-bottom: 0.25rem;
}

.unified-editor .prose blockquote {
  border-left: 4px solid #3B82F6;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6B7280;
}

.unified-editor .prose code {
  background-color: #F3F4F6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.unified-editor .prose pre {
  background-color: #1F2937;
  color: #F9FAFB;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.unified-editor .prose pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.unified-editor .prose a {
  color: #3B82F6;
  text-decoration: underline;
}

.unified-editor .prose a:hover {
  color: #1D4ED8;
}

/* 滚动条样式 */
.unified-editor ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.unified-editor ::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 3px;
}

.unified-editor ::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 3px;
}

.unified-editor ::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
