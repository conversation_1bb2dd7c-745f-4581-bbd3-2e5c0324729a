'use client';

import React, { useState, useEffect } from 'react';

import {
  Server,
  Database,
  Cpu,
  HardDrive,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import SystemPerformanceChart from '@/components/admin/charts/SystemPerformanceChart';
import ResourceDistributionChart from '@/components/admin/charts/ResourceDistributionChart';

// 使用自定义图表组件，避免Recharts的SSR问题

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
  database: {
    connections: number;
    maxConnections: number;
    queryTime: number;
  };
  uptime: number;
  status: 'healthy' | 'warning' | 'critical';
}

interface ChartData {
  time: string;
  cpu: number;
  memory: number;
  network: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

const SystemAnalyticsPage: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);

  useEffect(() => {
    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const fetchSystemMetrics = async () => {
    try {
      // 生成动态变化的数据，确保每次刷新都有变化
      const timestamp = Date.now();
      const timeVariation = Math.sin(timestamp / 60000) * 20; // 基于时间的变化

      const mockMetrics: SystemMetrics = {
        cpu: {
          usage: Math.max(10, Math.min(95, 45 + timeVariation + (Math.random() - 0.5) * 30)),
          cores: 8,
          temperature: Math.max(35, Math.min(75, 50 + (Math.random() - 0.5) * 20))
        },
        memory: {
          used: Math.max(2, Math.min(14, 8 + (Math.random() - 0.5) * 6)),
          total: 16,
          percentage: 0 // 将在下面计算
        },
        disk: {
          used: Math.max(100, Math.min(450, 250 + (Math.random() - 0.5) * 100)),
          total: 500,
          percentage: 0 // 将在下面计算
        },
        network: {
          inbound: Math.max(5, Math.random() * 150 + timeVariation),
          outbound: Math.max(2, Math.random() * 80 + timeVariation * 0.5)
        },
        database: {
          connections: Math.floor(Math.random() * 60) + 15,
          maxConnections: 100,
          queryTime: Math.max(5, Math.random() * 120 + 20)
        },
        uptime: 86400 * 15 + Math.random() * 86400, // ~15 days
        status: 'healthy'
      };

      // 计算百分比
      mockMetrics.memory.percentage = (mockMetrics.memory.used / mockMetrics.memory.total) * 100;
      mockMetrics.disk.percentage = (mockMetrics.disk.used / mockMetrics.disk.total) * 100;

      // 生成更真实的图表数据，确保数据有明显变化
      const now = new Date();
      const newChartData: ChartData[] = [];

      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        const hourFactor = Math.sin((23 - i) / 24 * Math.PI * 2) * 20; // 24小时周期变化
        const randomVariation = (Math.random() - 0.5) * 25; // 随机变化

        newChartData.push({
          time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
          cpu: Math.max(10, Math.min(90, 50 + hourFactor + randomVariation)),
          memory: Math.max(20, Math.min(85, 60 + hourFactor * 0.8 + randomVariation * 0.8)),
          network: Math.max(5, Math.min(95, 40 + hourFactor * 1.2 + randomVariation))
        });
      }

      // 生成动态的系统资源分类数据
      const categories = ['API Requests', 'Database Queries', 'Cache Operations', 'File Storage', 'Network Traffic'];
      const baseValues = [75, 60, 85, 45, 55]; // 基础值
      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

      const newCategoryData: CategoryData[] = categories.map((name, index) => ({
        name,
        value: Math.max(10, Math.min(100, (baseValues[index] || 50) + (Math.random() - 0.5) * 30)),
        color: colors[index] || '#6B7280'
      }));

      setMetrics(mockMetrics);
      setChartData(newChartData);
      setCategoryData(newCategoryData);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    return `${bytes.toFixed(1)} GB`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertTriangle className="w-5 h-5" />;
      case 'critical': return <AlertTriangle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">System Analytics</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">System Analytics</h1>
        </div>
        <div className="text-center py-12">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load system metrics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h1 className="text-3xl font-bold text-gray-900">System Analytics</h1>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Clock className="w-4 h-4" />
            <span>Auto-refresh: {refreshInterval}s</span>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={10}>10s</option>
              <option value={30}>30s</option>
              <option value={60}>1m</option>
              <option value={300}>5m</option>
            </select>
            <button
              onClick={fetchSystemMetrics}
              className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Now</span>
            </button>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Server className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">System Status</h2>
              <p className="text-gray-600">Overall system health and uptime</p>
            </div>
          </div>
          <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${getStatusColor(metrics.status)}`}>
            {getStatusIcon(metrics.status)}
            <span className="font-medium capitalize">{metrics.status}</span>
          </div>
        </div>
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Uptime</span>
              <TrendingUp className="w-4 h-4 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {formatUptime(metrics.uptime)}
            </p>
          </div>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Last Restart</span>
              <Clock className="w-4 h-4 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {Math.floor(metrics.uptime / 86400)} days ago
            </p>
          </div>
        </div>
      </div>

      {/* Resource Usage */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* CPU Usage */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Cpu className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">CPU Usage</h3>
            </div>
            <span className="text-3xl font-bold text-blue-600">
              {metrics.cpu.usage.toFixed(1)}%
            </span>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Usage</span>
              <span className="font-medium">{metrics.cpu.usage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${metrics.cpu.usage}%` }}
              ></div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm pt-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Cores:</span>
                <span className="font-medium">{metrics.cpu.cores}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Temp:</span>
                <span className="font-medium">{metrics.cpu.temperature.toFixed(1)}°C</span>
              </div>
            </div>
          </div>
        </div>

        {/* Memory Usage */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Activity className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Memory Usage</h3>
            </div>
            <span className="text-3xl font-bold text-green-600">
              {metrics.memory.percentage.toFixed(1)}%
            </span>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Used</span>
              <span className="font-medium">
                {formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.total)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${metrics.memory.percentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Performance Trend */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Performance Trend</h3>
                <p className="text-sm text-gray-600">Last 24 hours</p>
              </div>
            </div>
          </div>
          <div className="h-80">
            {chartData.length > 0 ? (
              <SystemPerformanceChart data={chartData} />
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Loading chart data...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Resource Distribution */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Database className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Resource Distribution</h3>
                <p className="text-sm text-gray-600">Current usage breakdown</p>
              </div>
            </div>
          </div>
          <div className="h-80">
            {categoryData.length > 0 ? (
              <ResourceDistributionChart data={categoryData} />
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
                <div className="text-center">
                  <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Loading chart data...</p>
                </div>
              </div>
            )}
          </div>

        </div>
      </div>
    </div>
  );
};

export default SystemAnalyticsPage;
