'use client';

import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Lock, 
  AlertTriangle, 
  Key, 
  Eye, 
  EyeOff, 
  Save, 
  RefreshCw,
  Clock,
  Users,
  Activity,
  Ban,
  CheckCircle,
  XCircle,
  Search,
  Filter
} from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';

// 安全设置接口
interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
    maxAge: number; // 密码最大有效期（天）
  };
  loginSecurity: {
    maxAttempts: number;
    lockoutDuration: number; // 锁定时长（分钟）
    sessionTimeout: number; // 会话超时（分钟）
    requireTwoFactor: boolean;
  };
  accountSecurity: {
    autoLockInactive: boolean;
    inactivityDays: number;
    requireEmailVerification: boolean;
    allowMultipleSessions: boolean;
  };
}

// 安全日志接口
interface SecurityLog {
  id: string;
  userId: string;
  username: string;
  email: string;
  action: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  status: 'success' | 'failed' | 'blocked';
  details?: string;
}

// 被锁定账户接口
interface LockedAccount {
  id: string;
  username: string;
  email: string;
  lockReason: string;
  lockedAt: Date;
  unlockAt?: Date;
  failedAttempts: number;
  lastAttemptIp: string;
}

const AccountSecurityPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'settings' | 'logs' | 'locked'>('settings');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  
  // 安全设置状态
  const [settings, setSettings] = useState<SecuritySettings>({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: false,
      maxAge: 90
    },
    loginSecurity: {
      maxAttempts: 5,
      lockoutDuration: 15,
      sessionTimeout: 60,
      requireTwoFactor: false
    },
    accountSecurity: {
      autoLockInactive: true,
      inactivityDays: 180,
      requireEmailVerification: true,
      allowMultipleSessions: false
    }
  });

  // 安全日志状态
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [logFilter, setLogFilter] = useState('all');
  const [logSearch, setLogSearch] = useState('');

  // 被锁定账户状态
  const [lockedAccounts, setLockedAccounts] = useState<LockedAccount[]>([]);

  // 初始化数据
  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    setLoading(true);
    try {
      // 模拟加载安全日志数据
      const mockLogs: SecurityLog[] = [
        {
          id: '1',
          userId: 'user1',
          username: 'john_doe',
          email: '<EMAIL>',
          action: 'Login Attempt',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          status: 'success'
        },
        {
          id: '2',
          userId: 'user2',
          username: 'jane_smith',
          email: '<EMAIL>',
          action: 'Failed Login',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date(Date.now() - 1000 * 60 * 45),
          status: 'failed',
          details: 'Invalid password'
        },
        {
          id: '3',
          userId: 'user3',
          username: 'suspicious_user',
          email: '<EMAIL>',
          action: 'Account Locked',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date(Date.now() - 1000 * 60 * 60),
          status: 'blocked',
          details: 'Too many failed attempts'
        }
      ];

      // 模拟被锁定账户数据
      const mockLockedAccounts: LockedAccount[] = [
        {
          id: '1',
          username: 'suspicious_user',
          email: '<EMAIL>',
          lockReason: 'Too many failed login attempts',
          lockedAt: new Date(Date.now() - 1000 * 60 * 60),
          unlockAt: new Date(Date.now() + 1000 * 60 * 15),
          failedAttempts: 5,
          lastAttemptIp: '*************'
        }
      ];

      setSecurityLogs(mockLogs);
      setLockedAccounts(mockLockedAccounts);
    } catch (error) {
      console.error('加载安全数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存安全设置
  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // 这里应该调用API保存设置
      console.log('保存安全设置:', settings);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('安全设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 解锁账户
  const handleUnlockAccount = async (accountId: string) => {
    if (!confirm('确定要解锁此账户吗？')) return;
    
    setLoading(true);
    try {
      // 这里应该调用API解锁账户
      console.log('解锁账户:', accountId);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 从列表中移除已解锁的账户
      setLockedAccounts(prev => prev.filter(account => account.id !== accountId));
      
      alert('账户已解锁');
    } catch (error) {
      console.error('解锁账户失败:', error);
      alert('解锁失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 过滤安全日志
  const filteredLogs = securityLogs.filter(log => {
    const matchesFilter = logFilter === 'all' || log.status === logFilter;
    const matchesSearch = logSearch === '' || 
      log.username.toLowerCase().includes(logSearch.toLowerCase()) ||
      log.email.toLowerCase().includes(logSearch.toLowerCase()) ||
      log.action.toLowerCase().includes(logSearch.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'blocked':
        return <Ban className="w-4 h-4 text-orange-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🔒 Account Security
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive account security management, monitoring, and policy configuration
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <CompactDatePicker
              value={currentDateRange}
              onChange={setCurrentDateRange}
              placeholder="Select date range"
              className="w-full sm:w-56"
            />
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'settings', label: '🛡️ Security Settings', icon: Shield },
              { id: 'logs', label: '📋 Security Logs', icon: Activity },
              { id: 'locked', label: '🔒 Locked Accounts', icon: Ban }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'settings' && (
        <div className="space-y-6">
          {/* 密码策略设置 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <Key className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Password Policy</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Password Length
                </label>
                <input
                  type="number"
                  min="6"
                  max="32"
                  value={settings.passwordPolicy.minLength}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    passwordPolicy: {
                      ...prev.passwordPolicy,
                      minLength: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password Max Age (Days)
                </label>
                <input
                  type="number"
                  min="30"
                  max="365"
                  value={settings.passwordPolicy.maxAge}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    passwordPolicy: {
                      ...prev.passwordPolicy,
                      maxAge: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <h4 className="text-lg font-semibold text-gray-900">Password Requirements</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { key: 'requireUppercase', label: 'Require Uppercase Letters' },
                  { key: 'requireLowercase', label: 'Require Lowercase Letters' },
                  { key: 'requireNumbers', label: 'Require Numbers' },
                  { key: 'requireSymbols', label: 'Require Special Characters' }
                ].map(requirement => (
                  <label key={requirement.key} className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={settings.passwordPolicy[requirement.key as keyof typeof settings.passwordPolicy] as boolean}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        passwordPolicy: {
                          ...prev.passwordPolicy,
                          [requirement.key]: e.target.checked
                        }
                      }))}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{requirement.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* 登录安全设置 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                <Lock className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Login Security</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Login Attempts
                </label>
                <input
                  type="number"
                  min="3"
                  max="10"
                  value={settings.loginSecurity.maxAttempts}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    loginSecurity: {
                      ...prev.loginSecurity,
                      maxAttempts: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lockout Duration (Minutes)
                </label>
                <input
                  type="number"
                  min="5"
                  max="60"
                  value={settings.loginSecurity.lockoutDuration}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    loginSecurity: {
                      ...prev.loginSecurity,
                      lockoutDuration: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Session Timeout (Minutes)
                </label>
                <input
                  type="number"
                  min="15"
                  max="480"
                  value={settings.loginSecurity.sessionTimeout}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    loginSecurity: {
                      ...prev.loginSecurity,
                      sessionTimeout: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.loginSecurity.requireTwoFactor}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    loginSecurity: {
                      ...prev.loginSecurity,
                      requireTwoFactor: e.target.checked
                    }
                  }))}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Require Two-Factor Authentication</span>
              </label>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveSettings}
              disabled={loading}
              className="flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{loading ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>
        </div>
      )}

      {activeTab === 'logs' && (
        <div className="space-y-6">
          {/* 搜索和过滤 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search by username, email, or action..."
                    value={logSearch}
                    onChange={(e) => setLogSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={logFilter}
                  onChange={(e) => setLogFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="success">Success</option>
                  <option value="failed">Failed</option>
                  <option value="blocked">Blocked</option>
                </select>
              </div>
            </div>
          </div>

          {/* 安全日志列表 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Security Activity Logs
              </h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Time
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{log.username}</div>
                          <div className="text-sm text-gray-500">{log.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{log.action}</div>
                        {log.details && (
                          <div className="text-sm text-gray-500">{log.details}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(log.status)}
                          <span className={`text-sm font-medium ${
                            log.status === 'success' ? 'text-green-600' :
                            log.status === 'failed' ? 'text-red-600' :
                            'text-orange-600'
                          }`}>
                            {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {log.ipAddress}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatTime(log.timestamp)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'locked' && (
        <div className="space-y-6">
          {/* 被锁定账户列表 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 flex items-center">
                <Ban className="w-5 h-5 mr-2" />
                Locked Accounts ({lockedAccounts.length})
              </h3>
            </div>
            
            {lockedAccounts.length === 0 ? (
              <div className="p-8 text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">No Locked Accounts</h4>
                <p className="text-gray-600">All accounts are currently accessible.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Account
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Lock Reason
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Failed Attempts
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Locked At
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {lockedAccounts.map((account) => (
                      <tr key={account.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{account.username}</div>
                            <div className="text-sm text-gray-500">{account.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{account.lockReason}</div>
                          <div className="text-sm text-gray-500">IP: {account.lastAttemptIp}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {account.failedAttempts} attempts
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatTime(account.lockedAt)}
                          {account.unlockAt && (
                            <div className="text-xs text-gray-500">
                              Auto-unlock: {formatTime(account.unlockAt)}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => handleUnlockAccount(account.id)}
                            disabled={loading}
                            className="text-blue-600 hover:text-blue-900 text-sm font-medium disabled:opacity-50"
                          >
                            Unlock Account
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountSecurityPage;
