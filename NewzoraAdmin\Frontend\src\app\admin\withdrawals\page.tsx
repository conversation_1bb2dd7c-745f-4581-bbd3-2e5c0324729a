'use client';

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  Users,
  TrendingUp,
  Filter,
  Shield,
  Zap,
  Mail,
  Phone,
  Camera,
  FileText,
  Eye,
  Settings,
  Plus,
  Edit
} from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';
import { filterByDateRange } from '@/utils/dateFilters';

/**
 * 提现管理页面 - 专门管理用户提现申请、收款账户操作和提现统计
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const WithdrawalsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [selectedWithdrawals, setSelectedWithdrawals] = useState<string[]>([]);
  const [withdrawalFilter, setWithdrawalFilter] = useState('all');
  const [showPaymentMethodModal, setShowPaymentMethodModal] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);

  // 提现统计数据
  const [withdrawalStats, setWithdrawalStats] = useState({
    totalWithdrawals: 45200,
    pendingWithdrawals: 8900,
    completedWithdrawals: 36300,
    rejectedWithdrawals: 2400,
    totalUsers: 1240,
    averageAmount: 365
  });

  // 收款方式配置（基于前台主站）
  const paymentMethods = {
    bank_card: {
      id: 'bank_card',
      name: 'Bank Transfer',
      fee: 2.5,
      time: '1-3 business days',
      icon: CreditCard,
      description: 'Direct transfer to bank account',
      popular: true,
      security: 'High',
      requirements: ['Bank account number', 'Routing number', 'Account holder name', 'Bank name'],
      verificationLevel: 'standard'
    },
    paypal: {
      id: 'paypal',
      name: 'PayPal',
      fee: 3.5,
      time: 'Instant',
      icon: DollarSign,
      description: 'Fast and secure PayPal transfer',
      popular: false,
      security: 'High',
      requirements: ['PayPal email address', 'Account verification'],
      verificationLevel: 'standard'
    },
    crypto_btc: {
      id: 'crypto_btc',
      name: 'Bitcoin (BTC)',
      fee: 1.5,
      time: '30min-2hrs',
      icon: Shield,
      description: 'Cryptocurrency transfer to wallet',
      popular: true,
      security: 'Very High',
      requirements: ['Bitcoin wallet address', 'Address verification'],
      verificationLevel: 'enhanced'
    },
    crypto_usdt: {
      id: 'crypto_usdt',
      name: 'USDT (TRC20)',
      fee: 1.0,
      time: '5-15min',
      icon: Zap,
      description: 'Fast stablecoin transfer',
      popular: true,
      security: 'Very High',
      requirements: ['USDT wallet address (TRC20)', 'Network verification'],
      verificationLevel: 'enhanced'
    },
    wise: {
      id: 'wise',
      name: 'Wise (TransferWise)',
      fee: 2.0,
      time: '1-2 business days',
      icon: TrendingUp,
      description: 'International money transfer',
      popular: false,
      security: 'High',
      requirements: ['Wise account email', 'Currency preference'],
      verificationLevel: 'standard'
    }
  };

  // 认证要素配置（基于前台主站的多维度安全认证系统）
  const verificationRequirements = {
    // 第一层：身份认证层 (Critical Level)
    identity: {
      biometric: {
        id: 'biometric',
        title: 'Biometric Authentication',
        description: 'Facial recognition and fingerprint verification',
        icon: Camera,
        required: true,
        securityLevel: 'critical',
        estimatedTime: '2-5 minutes',
        status: 'pending'
      },
      document: {
        id: 'document',
        title: 'Document Verification',
        description: 'Government ID and proof of address',
        icon: FileText,
        required: true,
        securityLevel: 'critical',
        estimatedTime: '5-10 minutes',
        status: 'pending'
      },
      video: {
        id: 'video',
        title: 'Live Video Verification',
        description: 'Real-time video call verification',
        icon: Eye,
        required: false,
        securityLevel: 'high',
        estimatedTime: '10-15 minutes',
        status: 'optional'
      }
    },
    // 第二层：联系方式验证
    contact: {
      email: {
        id: 'email',
        title: 'Email Verification',
        description: 'Email-based verification with secure tokens',
        icon: Mail,
        required: true,
        securityLevel: 'medium',
        estimatedTime: '1-2 minutes',
        status: 'pending'
      },
      phone: {
        id: 'phone',
        title: 'Phone Verification',
        description: 'SMS verification code',
        icon: Phone,
        required: true,
        securityLevel: 'medium',
        estimatedTime: '1-2 minutes',
        status: 'pending'
      }
    },
    // 第三层：交易安全层
    transaction: {
      limits: {
        id: 'limits',
        title: 'Transaction Limits',
        description: 'Dynamic transaction limit verification',
        icon: Shield,
        required: true,
        securityLevel: 'high',
        estimatedTime: 'Instant',
        status: 'pending'
      },
      cooling: {
        id: 'cooling',
        title: 'Security Cooling Period',
        description: 'Large transaction delay execution',
        icon: Clock,
        required: false,
        securityLevel: 'critical',
        estimatedTime: '24-72 hours',
        status: 'conditional'
      }
    }
  };

  // 生成支付详情的辅助函数
  const generatePaymentDetails = (methodKey: string) => {
    switch (methodKey) {
      case 'bank_card':
        return {
          bankName: ['Chase Bank', 'Bank of America', 'Wells Fargo', 'Citibank'][Math.floor(Math.random() * 4)],
          accountNumber: `****${Math.floor(Math.random() * 9000) + 1000}`,
          verified: Math.random() > 0.2
        };
      case 'paypal':
        return {
          email: `user${Math.floor(Math.random() * 1000)}@example.com`,
          verified: Math.random() > 0.1
        };
      case 'crypto_btc':
        return {
          address: `**********************************`,
          verified: Math.random() > 0.3
        };
      case 'crypto_usdt':
        return {
          address: `TQn9Y2khEsLMWD2YhUKXiMcPAlK467Zfn6`,
          network: 'TRC20',
          verified: Math.random() > 0.3
        };
      case 'wise':
        return {
          email: `user${Math.floor(Math.random() * 1000)}@example.com`,
          currency: ['USD', 'EUR', 'GBP'][Math.floor(Math.random() * 3)],
          verified: Math.random() > 0.2
        };
      default:
        return { verified: false };
    }
  };

  // 生成验证状态的辅助函数
  const generateVerificationStatus = () => {
    const statuses = ['completed', 'pending', 'failed', 'not_required'];
    return {
      identity: {
        biometric: statuses[Math.floor(Math.random() * 3)], // 不包括 not_required
        document: statuses[Math.floor(Math.random() * 3)]
      },
      contact: {
        email: statuses[Math.floor(Math.random() * 3)],
        phone: statuses[Math.floor(Math.random() * 3)]
      },
      transaction: {
        limits: statuses[Math.floor(Math.random() * 4)]
      }
    };
  };

  // 生成提现记录数据
  const generateWithdrawalRecords = () => {
    const records = [];
    const users = [
      { username: 'john_doe', email: '<EMAIL>', userId: 'user123' },
      { username: 'jane_smith', email: '<EMAIL>', userId: 'user456' },
      { username: 'mike_wilson', email: '<EMAIL>', userId: 'user789' },
      { username: 'sarah_jones', email: '<EMAIL>', userId: 'user321' },
      { username: 'david_brown', email: '<EMAIL>', userId: 'user654' },
      { username: 'lisa_garcia', email: '<EMAIL>', userId: 'user987' },
      { username: 'tom_wilson', email: '<EMAIL>', userId: 'user246' },
      { username: 'emma_davis', email: '<EMAIL>', userId: 'user135' }
    ];
    const methodKeys = Object.keys(paymentMethods);
    const statuses = ['pending', 'approved', 'completed', 'rejected', 'processing'];

    // 添加一些预定义记录
    const predefinedRecords = [
      {
        id: 'WD001',
        userId: 'user123',
        username: 'john_doe',
        email: '<EMAIL>',
        amount: 250,
        method: 'paypal',
        methodName: 'PayPal',
        status: 'pending',
        paymentDetails: {
          email: '<EMAIL>',
          verified: true
        },
        verificationStatus: {
          identity: { biometric: 'completed', document: 'completed' },
          contact: { email: 'completed', phone: 'completed' },
          transaction: { limits: 'completed' }
        },
        requestDate: '2024-01-15',
        processedDate: null,
        notes: 'First withdrawal request',
        adminNotes: '',
        user: 'John Doe',
        date: '2024-01-15',
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 'WD002',
        userId: 'user456',
        username: 'jane_smith',
        email: '<EMAIL>',
        amount: 500,
        method: 'bank_card',
        methodName: 'Bank Transfer',
        status: 'processing',
        paymentDetails: {
          bankName: 'Chase Bank',
          accountNumber: '****1234',
          verified: true
        },
        verificationStatus: {
          identity: { biometric: 'completed', document: 'completed' },
          contact: { email: 'completed', phone: 'completed' },
          transaction: { limits: 'completed' }
        },
        requestDate: '2024-01-14',
        processedDate: null,
        notes: 'Urgent withdrawal request',
        adminNotes: 'Verified account details',
        user: 'Jane Smith',
        date: '2024-01-14',
        createdAt: '2024-01-14T14:20:00Z'
      }
    ];

    records.push(...predefinedRecords);

    // 生成额外的随机记录
    for (let i = 3; i <= 50; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365));
      const user = users[Math.floor(Math.random() * users.length)];
      const methodKey = methodKeys[Math.floor(Math.random() * methodKeys.length)];
      const method = paymentMethods[methodKey];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      records.push({
        id: `WD${i.toString().padStart(3, '0')}`,
        userId: user.userId,
        username: user.username,
        email: user.email,
        amount: Math.floor(Math.random() * 1000) + 50,
        method: methodKey,
        methodName: method.name,
        status,
        paymentDetails: generatePaymentDetails(methodKey),
        verificationStatus: generateVerificationStatus(),
        requestDate: date.toISOString().split('T')[0],
        processedDate: status === 'completed' ? date.toISOString().split('T')[0] : null,
        notes: `Withdrawal request via ${method.name}`,
        adminNotes: status === 'completed' ? 'Processed successfully' :
                   status === 'rejected' ? 'Insufficient verification' : '',
        user: user.username.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        date: date.toISOString().split('T')[0],
        createdAt: date.toISOString()
      });
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  // 提现记录数据状态
  const [withdrawalRecords, setWithdrawalRecords] = useState(() => generateWithdrawalRecords());
  const [allWithdrawalRecords, setAllWithdrawalRecords] = useState(() => generateWithdrawalRecords());

  useEffect(() => {
    // 初始化数据
    const allRecords = generateWithdrawalRecords();
    setAllWithdrawalRecords(allRecords);
    setWithdrawalRecords(allRecords.slice(0, 20)); // 显示前20条

    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // 根据日期范围过滤提现记录
    if (allWithdrawalRecords.length > 0) {
      const filteredRecords = filterByDateRange(allWithdrawalRecords, dateRange, {
        dateField: 'createdAt'
      });
      setWithdrawalRecords(filteredRecords.slice(0, 20)); // 显示前20条
    }

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    setWithdrawalStats({
      totalWithdrawals: Math.floor(45200 * multiplier),
      pendingWithdrawals: Math.floor(8900 * multiplier),
      completedWithdrawals: Math.floor(36300 * multiplier),
      rejectedWithdrawals: Math.floor(2400 * multiplier),
      totalUsers: Math.floor(1240 * Math.max(0.5, multiplier)),
      averageAmount: Math.floor(365 * (1 + (Math.random() - 0.5) * 0.2))
    });
  };

  // 处理收款方式管理
  const handleManagePaymentMethod = (record: any) => {
    setSelectedRecord(record);
    setShowPaymentMethodModal(true);
  };

  // 处理验证要素管理
  const handleManageVerification = (record: any) => {
    setSelectedRecord(record);
    setShowVerificationModal(true);
  };

  // 处理提现操作
  const handleApproveWithdrawal = async (id: string) => {
    try {
      // 更新提现状态为已批准
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'approved',
          processedDate: new Date().toISOString().split('T')[0],
          adminNotes: 'Approved by admin'
        } : record
      ));

      // 更新统计数据
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setWithdrawalStats(prev => ({
          ...prev,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount
        }));
      }

      alert('Withdrawal approved successfully!');
    } catch (error) {
      console.error('Failed to approve withdrawal:', error);
      alert('Failed to approve withdrawal');
    }
  };

  const handleCompleteWithdrawal = async (id: string) => {
    const transactionId = prompt('Please enter transaction ID or confirmation number:');
    if (!transactionId) return;

    try {
      // 更新提现状态为已完成
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'completed',
          processedDate: new Date().toISOString().split('T')[0],
          adminNotes: `Completed - Transaction ID: ${transactionId}`
        } : record
      ));

      // 更新统计数据
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setWithdrawalStats(prev => ({
          ...prev,
          completedWithdrawals: prev.completedWithdrawals + record.amount,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount
        }));
      }

      alert('Withdrawal completed successfully!');
    } catch (error) {
      console.error('Failed to complete withdrawal:', error);
      alert('Failed to complete withdrawal');
    }
  };

  const handleRejectWithdrawal = async (id: string) => {
    const reason = prompt('Please enter rejection reason:');
    if (!reason) return;

    try {
      // 更新提现状态为已拒绝
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'rejected',
          processedDate: new Date().toISOString().split('T')[0],
          adminNotes: `Rejected - Reason: ${reason}`
        } : record
      ));

      // 更新统计数据
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setWithdrawalStats(prev => ({
          ...prev,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount,
          rejectedWithdrawals: prev.rejectedWithdrawals + record.amount
        }));
      }

      alert('Withdrawal rejected successfully!');
    } catch (error) {
      console.error('Failed to reject withdrawal:', error);
      alert('Failed to reject withdrawal');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Withdrawal Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              💳 Withdrawal Management
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive withdrawal processing, account management, and payment analytics
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <CompactDatePicker
              value={currentDateRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range"
              className="w-full sm:w-56"
            />
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Withdrawals</p>
              <p className="text-2xl font-bold text-gray-900">${withdrawalStats.totalWithdrawals.toLocaleString()}</p>
              <p className="text-xs text-blue-600 mt-1">All time</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">${withdrawalStats.pendingWithdrawals.toLocaleString()}</p>
              <p className="text-xs text-yellow-600 mt-1">Awaiting approval</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">${withdrawalStats.completedWithdrawals.toLocaleString()}</p>
              <p className="text-xs text-green-600 mt-1">Successfully processed</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">${withdrawalStats.rejectedWithdrawals.toLocaleString()}</p>
              <p className="text-xs text-red-600 mt-1">Failed requests</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
              <XCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{withdrawalStats.totalUsers.toLocaleString()}</p>
              <p className="text-xs text-purple-600 mt-1">Active withdrawers</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Amount</p>
              <p className="text-2xl font-bold text-gray-900">${withdrawalStats.averageAmount}</p>
              <p className="text-xs text-teal-600 mt-1">Per withdrawal</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 提现记录表格 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                <CreditCard className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Withdrawal Records</h3>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={withdrawalFilter}
                onChange={(e) => setWithdrawalFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="approved">Approved</option>
                <option value="completed">Completed</option>
                <option value="rejected">Rejected</option>
              </select>
              <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center text-sm">
                <Filter className="w-4 h-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto max-w-full">
          <table className="w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedWithdrawals(withdrawalRecords.map(r => r.id));
                      } else {
                        setSelectedWithdrawals([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '200px' }}>
                  User
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Amount
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '180px' }}>
                  Payment Method
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '140px' }}>
                  Verification
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  Status
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '160px' }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {withdrawalRecords
                .filter(record => withdrawalFilter === 'all' || record.status === withdrawalFilter)
                .map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-3 py-3 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedWithdrawals.includes(record.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedWithdrawals(prev => [...prev, record.id]);
                        } else {
                          setSelectedWithdrawals(prev => prev.filter(id => id !== record.id));
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-medium text-xs">
                            {(record.user || record.username).charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3 min-w-0 flex-1">
                        <div className="text-sm font-medium text-gray-900 truncate">{record.user || record.username}</div>
                        <div className="text-xs text-gray-500 truncate">{record.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">${record.amount}</div>
                    <div className="text-xs text-gray-500">USD</div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      {paymentMethods[record.method] && (() => {
                        const IconComponent = paymentMethods[record.method].icon;
                        return <IconComponent className="w-4 h-4 mr-2 text-gray-500" />;
                      })()}
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {record.methodName || paymentMethods[record.method]?.name || record.method}
                        </div>
                        <div className="text-xs text-gray-500">
                          {record.paymentDetails?.verified ? (
                            <span className="text-green-600">✓ Verified</span>
                          ) : (
                            <span className="text-red-600">⚠ Unverified</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <div className="flex space-x-1">
                      {record.verificationStatus && (
                        <>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            record.verificationStatus.identity?.biometric === 'completed' ? 'bg-green-100 text-green-800' :
                            record.verificationStatus.identity?.biometric === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            ID
                          </span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            record.verificationStatus.contact?.email === 'completed' ? 'bg-green-100 text-green-800' :
                            record.verificationStatus.contact?.email === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            Email
                          </span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            record.verificationStatus.contact?.phone === 'completed' ? 'bg-green-100 text-green-800' :
                            record.verificationStatus.contact?.phone === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            Phone
                          </span>
                        </>
                      )}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      record.status === 'completed' ? 'bg-green-100 text-green-800' :
                      record.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                      record.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                      record.status === 'pending' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
                    {new Date(record.requestDate).toLocaleDateString('en-US')}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm font-medium">
                    <div className="flex flex-col space-y-2">
                      {/* 主要操作按钮 */}
                      <div className="flex space-x-2">
                        {record.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleApproveWithdrawal(record.id)}
                              className="text-green-600 hover:text-green-900 flex items-center text-xs"
                            >
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Approve
                            </button>
                            <button
                              onClick={() => handleRejectWithdrawal(record.id)}
                              className="text-red-600 hover:text-red-900 flex items-center text-xs"
                            >
                              <XCircle className="w-3 h-3 mr-1" />
                              Reject
                            </button>
                          </>
                        )}
                        {record.status === 'approved' && (
                          <button
                            onClick={() => handleCompleteWithdrawal(record.id)}
                            className="text-blue-600 hover:text-blue-900 flex items-center text-xs"
                          >
                            <CreditCard className="w-3 h-3 mr-1" />
                            Complete
                          </button>
                        )}
                        {record.status === 'processing' && (
                          <button
                            onClick={() => handleCompleteWithdrawal(record.id)}
                            className="text-blue-600 hover:text-blue-900 flex items-center text-xs"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Mark Complete
                          </button>
                        )}
                        {(record.status === 'completed' || record.status === 'rejected') && (
                          <span className="text-gray-400 text-xs">
                            {record.processedDate && new Date(record.processedDate).toLocaleDateString('en-US')}
                          </span>
                        )}
                      </div>

                      {/* 管理操作按钮 */}
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleManagePaymentMethod(record)}
                          className="text-purple-600 hover:text-purple-900 flex items-center text-xs"
                          title="Manage Payment Method"
                        >
                          <Settings className="w-3 h-3 mr-1" />
                          Payment
                        </button>
                        <button
                          onClick={() => handleManageVerification(record)}
                          className="text-indigo-600 hover:text-indigo-900 flex items-center text-xs"
                          title="Manage Verification"
                        >
                          <Shield className="w-3 h-3 mr-1" />
                          Verify
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {withdrawalRecords.filter(record => withdrawalFilter === 'all' || record.status === withdrawalFilter).length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No withdrawal records found for the selected filter.</p>
          </div>
        )}
      </div>

      {/* 收款方式管理模态框 */}
      {showPaymentMethodModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Payment Method Management</h3>
              <button
                onClick={() => setShowPaymentMethodModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* 当前收款方式信息 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Current Payment Method</h4>
                <div className="flex items-center space-x-3">
                  {paymentMethods[selectedRecord.method] && (() => {
                    const IconComponent = paymentMethods[selectedRecord.method].icon;
                    return <IconComponent className="w-6 h-6 text-gray-600" />;
                  })()}
                  <div>
                    <div className="font-medium">{selectedRecord.methodName}</div>
                    <div className="text-sm text-gray-600">
                      Fee: {paymentMethods[selectedRecord.method]?.fee}% |
                      Time: {paymentMethods[selectedRecord.method]?.time}
                    </div>
                  </div>
                </div>

                {/* 支付详情 */}
                <div className="mt-3 p-3 bg-white rounded border">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Payment Details</h5>
                  {selectedRecord.paymentDetails && Object.entries(selectedRecord.paymentDetails).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 可用收款方式 */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Available Payment Methods</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(paymentMethods).map(([key, method]) => {
                    const isCurrentMethod = selectedRecord.method === key;
                    return (
                      <button
                        key={key}
                        onClick={() => {
                          // 更新选中的收款方式
                          setSelectedRecord(prev => ({
                            ...prev,
                            method: key,
                            methodName: method.name
                          }));
                        }}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 text-left hover:shadow-md ${
                          isCurrentMethod
                            ? 'border-blue-500 bg-blue-50 shadow-lg'
                            : 'border-gray-200 hover:border-blue-300 bg-white'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          {(() => {
                            const IconComponent = method.icon;
                            return <IconComponent className={`w-5 h-5 ${isCurrentMethod ? 'text-blue-600' : 'text-gray-600'}`} />;
                          })()}
                          <div className="flex-1">
                            <div className={`font-medium text-sm ${isCurrentMethod ? 'text-blue-900' : 'text-gray-900'}`}>
                              {method.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {method.fee}% fee • {method.time}
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {method.description}
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            {method.popular && (
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded font-medium">
                                Popular
                              </span>
                            )}
                            {isCurrentMethod && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium">
                                Selected
                              </span>
                            )}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => setShowPaymentMethodModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // 更新提现记录的收款方式
                    setWithdrawalRecords(prev => prev.map(record =>
                      record.id === selectedRecord.id
                        ? {
                            ...record,
                            method: selectedRecord.method,
                            methodName: selectedRecord.methodName,
                            paymentDetails: {
                              ...record.paymentDetails,
                              // 这里可以根据不同的收款方式更新相应的详情
                              verified: false // 切换收款方式后需要重新验证
                            }
                          }
                        : record
                    ));

                    // 显示成功消息
                    alert(`Payment method updated to ${selectedRecord.methodName} successfully!`);
                    setShowPaymentMethodModal(false);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Update Method
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 验证要素管理模态框 */}
      {showVerificationModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">Verification Management</h3>
              <button
                onClick={() => setShowVerificationModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* 验证要素分类 */}
              {Object.entries(verificationRequirements).map(([categoryKey, category]) => (
                <div key={categoryKey} className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-4 capitalize">
                    {categoryKey.replace('_', ' ')} Verification
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(category).map(([itemKey, item]) => {
                      const currentStatus = selectedRecord.verificationStatus?.[categoryKey]?.[itemKey] || 'pending';

                      return (
                        <div key={itemKey} className="bg-white rounded-lg p-4 border">
                          <div className="flex items-start space-x-3">
                            {(() => {
                              const IconComponent = item.icon;
                              return <IconComponent className="w-5 h-5 text-gray-600 mt-1" />;
                            })()}
                            <div className="flex-1">
                              <div className="font-medium text-sm">{item.title}</div>
                              <div className="text-xs text-gray-600 mb-2">{item.description}</div>

                              <div className="flex items-center justify-between">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  currentStatus === 'completed' ? 'bg-green-100 text-green-800' :
                                  currentStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                  currentStatus === 'failed' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
                                </span>

                                {item.required && (
                                  <span className="text-xs text-red-600 font-medium">Required</span>
                                )}
                              </div>

                              <div className="text-xs text-gray-500 mt-1">
                                Est. time: {item.estimatedTime}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  onClick={() => setShowVerificationModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    // 这里可以添加重新验证的逻辑
                    alert('Verification process initiated');
                    setShowVerificationModal(false);
                  }}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  Initiate Verification
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WithdrawalsPage;
