
'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  TrendingUp,
  TrendingDown,
  Globe,
  Calendar,
  MapPin,
  UserCheck,
  Activity,
  Clock,
  Smartphone,
  Monitor,
  Tablet,
  Heart,
  Star,
  Eye,
  MessageCircle,
  Share2,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON>hart,
  Target,
  Zap
} from 'lucide-react';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';

/**
 * 用户分析页面 - 多维度用户数据分析
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */

interface UserDemographics {
  age: { range: string; count: number; percentage: number; color: string }[];
  gender: { type: string; count: number; percentage: number; color: string }[];
  location: { country: string; city: string; count: number; percentage: number }[];
  education: { level: string; count: number; percentage: number; color: string }[];
  occupation: { type: string; count: number; percentage: number; color: string }[];
  income: { range: string; count: number; percentage: number; color: string }[];
}

interface UserBehavior {
  activeTime: { hour: number; count: number }[];
  sessionDuration: { range: string; count: number; percentage: number; color: string }[];
  deviceType: { type: string; count: number; percentage: number; color: string }[];
  platform: { name: string; count: number; percentage: number; color: string }[];
  contentPreference: { category: string; engagement: number; time: number; color: string }[];
  visitFrequency: { frequency: string; count: number; percentage: number; color: string }[];
}

interface UserEngagement {
  dailyActive: number;
  weeklyActive: number;
  monthlyActive: number;
  retention: { day: number; rate: number }[];
  churnRate: number;
  avgSessionTime: number;
  pageViews: number;
  interactions: number;
  conversionRate: number;
  lifetimeValue: number;
}

const UserAnalyticsPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [activeTab, setActiveTab] = useState<'demographics' | 'behavior' | 'engagement'>('demographics');

  const [demographics, setDemographics] = useState<UserDemographics | null>(null);
  const [behavior, setBehavior] = useState<UserBehavior | null>(null);
  const [engagement, setEngagement] = useState<UserEngagement | null>(null);

  // 生成模拟数据
  useEffect(() => {
    const generateData = () => {
      // Demographics data - Global perspective
      const mockDemographics: UserDemographics = {
        age: [
          { range: '18-24', count: 3420, percentage: 28.5, color: '#3B82F6' },
          { range: '25-34', count: 4180, percentage: 34.8, color: '#10B981' },
          { range: '35-44', count: 2890, percentage: 24.1, color: '#F59E0B' },
          { range: '45-54', count: 1120, percentage: 9.3, color: '#EF4444' },
          { range: '55+', count: 390, percentage: 3.3, color: '#8B5CF6' }
        ],
        gender: [
          { type: 'Male', count: 6840, percentage: 57.0, color: '#3B82F6' },
          { type: 'Female', count: 4920, percentage: 41.0, color: '#EC4899' },
          { type: 'Other', count: 240, percentage: 2.0, color: '#6B7280' }
        ],
        location: [
          { country: 'United States', city: 'New York', count: 2340, percentage: 19.5 },
          { country: 'United States', city: 'Los Angeles', count: 1890, percentage: 15.7 },
          { country: 'United Kingdom', city: 'London', count: 1560, percentage: 13.0 },
          { country: 'Canada', city: 'Toronto', count: 1230, percentage: 10.3 },
          { country: 'Germany', city: 'Berlin', count: 890, percentage: 7.4 },
          { country: 'France', city: 'Paris', count: 720, percentage: 6.0 },
          { country: 'Australia', city: 'Sydney', count: 650, percentage: 5.4 },
          { country: 'Japan', city: 'Tokyo', count: 580, percentage: 4.8 },
          { country: 'Singapore', city: 'Singapore', count: 450, percentage: 3.8 },
          { country: 'Others', city: 'Various', count: 1680, percentage: 14.1 }
        ],
        education: [
          { level: "Bachelor's Degree", count: 5460, percentage: 45.5, color: '#10B981' },
          { level: "Master's Degree", count: 3240, percentage: 27.0, color: '#3B82F6' },
          { level: 'High School', count: 2180, percentage: 18.2, color: '#F59E0B' },
          { level: 'PhD/Doctorate', count: 780, percentage: 6.5, color: '#8B5CF6' },
          { level: 'Other', count: 340, percentage: 2.8, color: '#6B7280' }
        ],
        occupation: [
          { type: 'Technology/IT', count: 3890, percentage: 32.4, color: '#3B82F6' },
          { type: 'Finance/Banking', count: 2340, percentage: 19.5, color: '#10B981' },
          { type: 'Education', count: 1890, percentage: 15.7, color: '#F59E0B' },
          { type: 'Healthcare', count: 1560, percentage: 13.0, color: '#EF4444' },
          { type: 'Student', count: 1230, percentage: 10.3, color: '#8B5CF6' },
          { type: 'Other', count: 1090, percentage: 9.1, color: '#6B7280' }
        ],
        income: [
          { range: 'Under $30K', count: 1890, percentage: 15.7, color: '#EF4444' },
          { range: '$30K-$50K', count: 3240, percentage: 27.0, color: '#F59E0B' },
          { range: '$50K-$80K', count: 4180, percentage: 34.8, color: '#10B981' },
          { range: '$80K-$150K', count: 2340, percentage: 19.5, color: '#3B82F6' },
          { range: 'Over $150K', count: 350, percentage: 3.0, color: '#8B5CF6' }
        ]
      };

      // Behavior data - Global user patterns
      const mockBehavior: UserBehavior = {
        activeTime: Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          count: Math.floor(Math.random() * 800) + 200 + (i >= 9 && i <= 22 ? 300 : 0)
        })),
        sessionDuration: [
          { range: '0-5 minutes', count: 2340, percentage: 19.5, color: '#EF4444' },
          { range: '5-15 minutes', count: 3890, percentage: 32.4, color: '#F59E0B' },
          { range: '15-30 minutes', count: 2890, percentage: 24.1, color: '#10B981' },
          { range: '30-60 minutes', count: 1890, percentage: 15.7, color: '#3B82F6' },
          { range: '60+ minutes', count: 990, percentage: 8.3, color: '#8B5CF6' }
        ],
        deviceType: [
          { type: 'Mobile', count: 8940, percentage: 74.5, color: '#10B981' },
          { type: 'Desktop', count: 2340, percentage: 19.5, color: '#3B82F6' },
          { type: 'Tablet', count: 720, percentage: 6.0, color: '#F59E0B' }
        ],
        platform: [
          { name: 'Android', count: 5460, percentage: 45.5, color: '#10B981' },
          { name: 'iOS', count: 3480, percentage: 29.0, color: '#6B7280' },
          { name: 'Windows', count: 1890, percentage: 15.7, color: '#3B82F6' },
          { name: 'macOS', count: 780, percentage: 6.5, color: '#8B5CF6' },
          { name: 'Other', count: 390, percentage: 3.3, color: '#EF4444' }
        ],
        contentPreference: [
          { category: 'Technology', engagement: 85, time: 12.5, color: '#3B82F6' },
          { category: 'Entertainment', engagement: 78, time: 15.2, color: '#EC4899' },
          { category: 'Education', engagement: 72, time: 18.7, color: '#10B981' },
          { category: 'News', engagement: 68, time: 8.3, color: '#F59E0B' },
          { category: 'Sports', engagement: 65, time: 9.8, color: '#EF4444' },
          { category: 'Lifestyle', engagement: 62, time: 11.2, color: '#8B5CF6' }
        ],
        visitFrequency: [
          { frequency: 'Daily', count: 4560, percentage: 38.0, color: '#10B981' },
          { frequency: 'Weekly', count: 3240, percentage: 27.0, color: '#3B82F6' },
          { frequency: 'Monthly', count: 2340, percentage: 19.5, color: '#F59E0B' },
          { frequency: 'Occasionally', count: 1560, percentage: 13.0, color: '#EF4444' },
          { frequency: 'First Time', count: 300, percentage: 2.5, color: '#8B5CF6' }
        ]
      };

      // 参与度数据
      const mockEngagement: UserEngagement = {
        dailyActive: 8940,
        weeklyActive: 11230,
        monthlyActive: 12000,
        retention: [
          { day: 1, rate: 85 },
          { day: 7, rate: 68 },
          { day: 14, rate: 52 },
          { day: 30, rate: 38 },
          { day: 60, rate: 28 },
          { day: 90, rate: 22 }
        ],
        churnRate: 15.2,
        avgSessionTime: 18.5,
        pageViews: 156780,
        interactions: 89340,
        conversionRate: 12.8,
        lifetimeValue: 285.6
      };

      setDemographics(mockDemographics);
      setBehavior(mockBehavior);
      setEngagement(mockEngagement);
      setLoading(false);
    };

    generateData();
  }, [currentDateRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl shadow-xl p-8 text-white">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
              👥 User Analytics
            </h1>
            <p className="text-lg text-blue-100">
              Multi-dimensional user data analysis and insights
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4 lg:mt-0">
            <CompactDatePicker
              value={currentDateRange}
              onChange={setCurrentDateRange}
              placeholder="Select date range"
              className="bg-white/10 border-white/20 text-white placeholder-white/70 w-56"
            />
          </div>
        </div>
      </div>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Daily Active Users</p>
              <p className="text-3xl font-bold text-gray-900">{engagement?.dailyActive.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-4 h-4 mr-1" />
                +12.5%
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Active Users</p>
              <p className="text-3xl font-bold text-gray-900">{engagement?.monthlyActive.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-4 h-4 mr-1" />
                +8.3%
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Session Time</p>
              <p className="text-3xl font-bold text-gray-900">{engagement?.avgSessionTime} min</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="w-4 h-4 mr-1" />
                +5.2%
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock className="w-8 h-8 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Churn Rate</p>
              <p className="text-3xl font-bold text-gray-900">{engagement?.churnRate}%</p>
              <p className="text-sm text-red-600 flex items-center mt-1">
                <TrendingDown className="w-4 h-4 mr-1" />
                -2.1%
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <Activity className="w-8 h-8 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-100">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'demographics', label: 'Demographics', icon: Users },
              { key: 'behavior', label: 'Behavior Analysis', icon: Activity },
              { key: 'engagement', label: 'Engagement', icon: Heart }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'demographics' && demographics && (
            <div className="space-y-8">
              {/* Age Distribution */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                    Age Distribution
                  </h3>
                  <div className="space-y-3">
                    {demographics.age.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.range}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Gender Distribution */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Users className="w-5 h-5 mr-2 text-purple-600" />
                    Gender Distribution
                  </h3>
                  <div className="space-y-3">
                    {demographics.gender.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.type}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 地理分布 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <MapPin className="w-5 h-5 mr-2 text-green-600" />
                  Geographic Distribution
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {demographics.location.map((item, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{item.city}</div>
                          <div className="text-sm text-gray-500">{item.country}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-blue-600">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 教育背景和职业分布 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Star className="w-5 h-5 mr-2 text-yellow-600" />
                    Education Level
                  </h3>
                  <div className="space-y-3">
                    {demographics.education.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.level}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-indigo-600" />
                    Occupation Distribution
                  </h3>
                  <div className="space-y-3">
                    {demographics.occupation.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.type}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 收入分布 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-emerald-600" />
                  Income Distribution
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  {demographics.income.map((item, index) => (
                    <div key={index} className="text-center p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                      <div className="w-12 h-12 mx-auto mb-2 rounded-full flex items-center justify-center" style={{ backgroundColor: item.color + '20' }}>
                        <div className="w-6 h-6 rounded-full" style={{ backgroundColor: item.color }}></div>
                      </div>
                      <div className="font-medium text-gray-900">{item.range}</div>
                      <div className="text-sm text-gray-500 mt-1">{item.count.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">{item.percentage}%</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          {activeTab === 'behavior' && behavior && (
            <div className="space-y-8">
              {/* 活跃时间分布 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-blue-600" />
                  24-Hour Activity Distribution
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-end justify-between h-32 space-x-1">
                    {behavior.activeTime.map((item, index) => (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div
                          className="bg-blue-500 rounded-t w-full transition-all duration-300 hover:bg-blue-600"
                          style={{ height: `${(item.count / Math.max(...behavior.activeTime.map(i => i.count))) * 100}%` }}
                          title={`${item.hour}:00 - ${item.count} active users`}
                        ></div>
                        <div className="text-xs text-gray-500 mt-1">{item.hour}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 设备和平台分布 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Smartphone className="w-5 h-5 mr-2 text-green-600" />
                    Device Types
                  </h3>
                  <div className="space-y-3">
                    {behavior.deviceType.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.type}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Monitor className="w-5 h-5 mr-2 text-purple-600" />
                    Operating Systems
                  </h3>
                  <div className="space-y-3">
                    {behavior.platform.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.name}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 会话时长和访问频率 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Clock className="w-5 h-5 mr-2 text-orange-600" />
                    Session Duration
                  </h3>
                  <div className="space-y-3">
                    {behavior.sessionDuration.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.range}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Zap className="w-5 h-5 mr-2 text-yellow-600" />
                    Visit Frequency
                  </h3>
                  <div className="space-y-3">
                    {behavior.visitFrequency.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-700">{item.frequency}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{item.count.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 内容偏好 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Heart className="w-5 h-5 mr-2 text-red-600" />
                  Content Preference Analysis
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {behavior.contentPreference.map((item, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                          <span className="font-medium text-gray-900">{item.category}</span>
                        </div>
                        <span className="text-sm font-bold text-blue-600">{item.engagement}%</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Avg. time: {item.time} minutes
                      </div>
                      <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            width: `${item.engagement}%`,
                            backgroundColor: item.color
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'engagement' && engagement && (
            <div className="space-y-8">
              {/* 用户留存率 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-green-600" />
                  User Retention Rate
                </h3>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    {engagement.retention.map((item, index) => (
                      <div key={index} className="text-center p-4 bg-white rounded-lg shadow-sm">
                        <div className="text-2xl font-bold text-blue-600">{item.rate}%</div>
                        <div className="text-sm text-gray-600">Day {item.day}</div>
                        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${item.rate}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 关键指标 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100">Page Views</p>
                      <p className="text-3xl font-bold">{engagement.pageViews.toLocaleString()}</p>
                    </div>
                    <Eye className="w-8 h-8 text-blue-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100">User Interactions</p>
                      <p className="text-3xl font-bold">{engagement.interactions.toLocaleString()}</p>
                    </div>
                    <MessageCircle className="w-8 h-8 text-green-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100">Conversion Rate</p>
                      <p className="text-3xl font-bold">{engagement.conversionRate}%</p>
                    </div>
                    <Target className="w-8 h-8 text-purple-200" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100">Lifetime Value</p>
                      <p className="text-3xl font-bold">${engagement.lifetimeValue}</p>
                    </div>
                    <Star className="w-8 h-8 text-orange-200" />
                  </div>
                </div>
              </div>

              {/* 用户活跃度对比 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-purple-600" />
                  User Activity Comparison
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">{engagement.dailyActive.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">Daily Active Users (DAU)</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">{engagement.weeklyActive.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">Weekly Active Users (WAU)</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">{engagement.monthlyActive.toLocaleString()}</div>
                    <div className="text-gray-600 mb-4">Monthly Active Users (MAU)</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserAnalyticsPage;
