'use client';

import { useState } from 'react';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  User, 
  MapPin, 
  FileText, 
  Phone, 
  Mail, 
  Video,
  Camera,
  Smartphone,
  Globe,
  TrendingUp,
  Lock,
  DollarSign,
  Clock,
  Eye,
  Fingerprint,
  Brain,
  Zap,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

export default function SecurityDetailsPage() {
  const [activeLayer, setActiveLayer] = useState('identity');

  const securityLayers = {
    identity: {
      title: 'Identity Authentication Layer',
      description: 'Critical biometric and document verification to prevent identity theft',
      color: 'from-red-500 to-pink-600',
      icon: User,
      features: [
        {
          name: 'Biometric Authentication',
          description: 'Advanced facial recognition with liveness detection',
          antiTheftFeatures: [
            '3D facial mapping prevents photo spoofing',
            'Eye movement tracking detects fake videos',
            'Infrared liveness detection',
            'Multiple angle verification',
            'Voice pattern recognition',
            'Fingerprint biometric backup'
          ],
          techSpecs: 'AI-powered with 99.9% accuracy, ISO 27001 certified'
        },
        {
          name: 'Document Verification',
          description: 'AI + Human expert document authenticity verification',
          antiTheftFeatures: [
            'OCR fraud detection algorithms',
            'Watermark and security feature verification',
            'Document aging analysis',
            'Cross-reference with government databases',
            'Hologram and microprint detection',
            'Expert human review for suspicious cases'
          ],
          techSpecs: 'Machine learning trained on 50M+ documents'
        },
        {
          name: 'Live Video Verification',
          description: 'Real-time video call with security specialists',
          antiTheftFeatures: [
            'Real-time interaction prevents deepfakes',
            'Document holding verification',
            'Background environment analysis',
            'Behavioral pattern assessment',
            'Voice stress analysis',
            'Multi-language security questions'
          ],
          techSpecs: 'Encrypted video calls with certified specialists'
        }
      ]
    },
    device: {
      title: 'Device & Location Security Layer',
      description: 'Advanced device fingerprinting and geolocation verification',
      color: 'from-blue-500 to-indigo-600',
      icon: Smartphone,
      features: [
        {
          name: 'Device Fingerprinting',
          description: 'Unique device identification and trust scoring',
          antiTheftFeatures: [
            'Hardware fingerprinting (CPU, GPU, RAM signatures)',
            'Browser fingerprinting (fonts, plugins, canvas)',
            'Network analysis (IP reputation, ISP verification)',
            'Device reputation scoring based on history',
            'Suspicious device pattern detection',
            'Device change alerts and verification'
          ],
          techSpecs: 'Over 200 device parameters analyzed'
        },
        {
          name: 'Geolocation Verification',
          description: 'Location-based security and anomaly detection',
          antiTheftFeatures: [
            'IP geolocation cross-verification',
            'GPS coordinates validation',
            'VPN/Proxy/Tor detection and blocking',
            'Travel pattern analysis and alerts',
            'Time zone consistency checks',
            'Impossible travel detection'
          ],
          techSpecs: 'Real-time global IP intelligence database'
        }
      ]
    },
    behavioral: {
      title: 'AI Behavioral Analysis Layer',
      description: 'Machine learning-powered user behavior analysis',
      color: 'from-purple-500 to-pink-600',
      icon: Brain,
      features: [
        {
          name: 'Behavioral Pattern Analysis',
          description: 'AI-powered user behavior analysis and anomaly detection',
          antiTheftFeatures: [
            'Typing pattern analysis (rhythm, speed, pressure)',
            'Mouse movement tracking and patterns',
            'Session behavior analysis',
            'Transaction pattern recognition',
            'Login time pattern analysis',
            'Navigation behavior fingerprinting'
          ],
          techSpecs: 'Neural networks trained on millions of user sessions'
        },
        {
          name: 'AI Risk Assessment',
          description: 'Machine learning-based fraud risk evaluation',
          antiTheftFeatures: [
            'Real-time ML fraud detection',
            'Risk scoring algorithm (1000+ factors)',
            'Anomaly detection across multiple dimensions',
            'Pattern recognition for known attack vectors',
            'Adaptive learning from new threats',
            'Ensemble model predictions'
          ],
          techSpecs: 'Gradient boosting with 95%+ fraud detection rate'
        }
      ]
    },
    mfa: {
      title: 'Multi-Factor Authentication Layer',
      description: 'Multiple verification channels for enhanced security',
      color: 'from-yellow-500 to-orange-600',
      icon: Lock,
      features: [
        {
          name: 'SMS Authentication',
          description: 'Mobile phone verification with advanced anti-SIM swap protection',
          antiTheftFeatures: [
            'SIM swap detection algorithms',
            'Phone number validation and history',
            'Carrier verification and reputation',
            'SMS delivery confirmation tracking',
            'Phone number change alerts',
            'Backup verification methods'
          ],
          techSpecs: 'Global SMS delivery with 99.9% reliability'
        },
        {
          name: 'Email Authentication',
          description: 'Email-based verification with domain security validation',
          antiTheftFeatures: [
            'Email domain reputation verification',
            'DKIM/SPF/DMARC validation',
            'Account age and activity verification',
            'Email pattern analysis for suspicious activity',
            'Encrypted token delivery',
            'Email change verification process'
          ],
          techSpecs: 'End-to-end encrypted email verification'
        },
        {
          name: 'Authenticator App (TOTP)',
          description: 'Time-based one-time passwords with offline capability',
          antiTheftFeatures: [
            'Time-based token generation (30-second windows)',
            'Device-specific secret key generation',
            'Offline capability prevents network attacks',
            'Backup codes for device loss scenarios',
            'QR code security with encryption',
            'Multiple device support'
          ],
          techSpecs: 'RFC 6238 compliant with AES-256 encryption'
        }
      ]
    },
    transaction: {
      title: 'Transaction Security Layer',
      description: 'Dynamic transaction monitoring and protection',
      color: 'from-green-500 to-emerald-600',
      icon: DollarSign,
      features: [
        {
          name: 'Dynamic Transaction Limits',
          description: 'AI-adjusted withdrawal limits based on risk assessment',
          antiTheftFeatures: [
            'Dynamic limit adjustment based on risk score',
            'Velocity checking (amount per time period)',
            'Amount pattern analysis and alerts',
            'Account balance verification',
            'Unusual amount detection',
            'Progressive limit increases with trust'
          ],
          techSpecs: 'Real-time risk-based limit calculation'
        },
        {
          name: 'Security Cooling Period',
          description: 'Mandatory waiting period for high-risk transactions',
          antiTheftFeatures: [
            'Time-delayed execution for large amounts',
            'Cancellation window for user protection',
            'Additional verification during waiting period',
            'Risk reassessment before execution',
            'Email/SMS alerts during cooling period',
            'Emergency cancellation procedures'
          ],
          techSpecs: '24-72 hour cooling period based on risk level'
        }
      ]
    }
  };

  const LayerIcon = securityLayers[activeLayer as keyof typeof securityLayers].icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <Link href="/withdraw" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Withdrawal
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            🛡️ Multi-Dimensional Security System
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl">
            Our advanced anti-theft protection system uses 5 layers of security with AI-powered fraud detection, 
            biometric verification, and behavioral analysis to ensure maximum protection of your funds.
          </p>
        </div>

        {/* Security Layer Tabs */}
        <div className="flex flex-wrap gap-2 mb-8">
          {Object.entries(securityLayers).map(([key, layer]) => {
            const Icon = layer.icon;
            return (
              <button
                key={key}
                onClick={() => setActiveLayer(key)}
                className={`flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                  activeLayer === key
                    ? `bg-gradient-to-r ${layer.color} text-white shadow-lg transform scale-105`
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                }`}
              >
                <Icon className="h-5 w-5 mr-2" />
                Layer {Object.keys(securityLayers).indexOf(key) + 1}
              </button>
            );
          })}
        </div>

        {/* Active Layer Details */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
          <div className="flex items-center mb-6">
            <div className={`w-16 h-16 bg-gradient-to-br ${securityLayers[activeLayer as keyof typeof securityLayers].color} rounded-2xl flex items-center justify-center mr-6 shadow-lg`}>
              <LayerIcon className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900">
                {securityLayers[activeLayer as keyof typeof securityLayers].title}
              </h2>
              <p className="text-gray-600 text-lg">
                {securityLayers[activeLayer as keyof typeof securityLayers].description}
              </p>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {securityLayers[activeLayer as keyof typeof securityLayers].features.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.name}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                    <Shield className="h-4 w-4 mr-2 text-red-600" />
                    Anti-Theft Features:
                  </h4>
                  <ul className="space-y-1">
                    {feature.antiTheftFeatures.map((antiFeature, idx) => (
                      <li key={idx} className="text-sm text-gray-700 flex items-start">
                        <CheckCircle className="h-3 w-3 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                        {antiFeature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-xs text-blue-800">
                    <strong>Tech Specs:</strong> {feature.techSpecs}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Security Statistics */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">99.9%</div>
            <div className="text-sm text-gray-600">Fraud Prevention Rate</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">5</div>
            <div className="text-sm text-gray-600">Security Layers</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">200+</div>
            <div className="text-sm text-gray-600">Security Parameters</div>
          </div>
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-sm text-gray-600">AI Monitoring</div>
          </div>
        </div>
      </div>
    </div>
  );
}
