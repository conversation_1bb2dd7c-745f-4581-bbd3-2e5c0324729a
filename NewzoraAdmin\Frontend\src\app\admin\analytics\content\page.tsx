
'use client';

import React, { useState } from 'react';
import { FileText, Eye, TrendingUp, MessageSquare, Heart, Calendar } from 'lucide-react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import CompactDatePicker, { DateRange } from '@/components/admin/common/CompactDatePicker';

const ContentAnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalArticles: 2340,
    totalViews: 156780,
    totalLikes: 12450,
    totalComments: 8920,
    averageReadTime: 4.2
  });

  // Mock data for content analytics
  const contentPublishData = [
    { date: '2023-06-01', articles: 12, views: 2450, comments: 86 },
    { date: '2023-06-02', articles: 15, views: 3120, comments: 112 },
    { date: '2023-06-03', articles: 8, views: 1980, comments: 64 },
    { date: '2023-06-04', articles: 18, views: 3890, comments: 142 },
    { date: '2023-06-05', articles: 14, views: 3210, comments: 98 },
    { date: '2023-06-06', articles: 20, views: 4250, comments: 156 },
    { date: '2023-06-07', articles: 16, views: 3680, comments: 124 },
  ];

  const categoryPerformanceData = [
    { category: 'Technology', articles: 145, views: 45230, avgEngagement: 4.2 },
    { category: 'Lifestyle', articles: 98, views: 32150, avgEngagement: 5.1 },
    { category: 'Business', articles: 76, views: 28940, avgEngagement: 3.8 },
    { category: 'Health', articles: 64, views: 24860, avgEngagement: 6.2 },
    { category: 'Entertainment', articles: 112, views: 38620, avgEngagement: 7.4 },
  ];

  const topContentData = [
    { 
      title: 'Getting Started with React Hooks', 
      views: 12450, 
      comments: 342, 
      likes: 856,
      publishedAt: '2023-05-28'
    },
    { 
      title: '10 JavaScript Tips for Better Performance', 
      views: 9870, 
      comments: 215, 
      likes: 642,
      publishedAt: '2023-06-01'
    },
    { 
      title: 'The Future of Web Development', 
      views: 8650, 
      comments: 187, 
      likes: 523,
      publishedAt: '2023-06-03'
    },
    { 
      title: 'Building Responsive Layouts with CSS Grid', 
      views: 7420, 
      comments: 156, 
      likes: 489,
      publishedAt: '2023-05-30'
    },
    { 
      title: 'Introduction to TypeScript', 
      views: 6980, 
      comments: 142, 
      likes: 421,
      publishedAt: '2023-06-05'
    },
  ];

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);
    setIsLoading(true);

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    // 模拟数据加载和更新
    setTimeout(() => {
      setStats({
        totalArticles: Math.floor(2340 * multiplier),
        totalViews: Math.floor(156780 * multiplier),
        totalLikes: Math.floor(12450 * multiplier),
        totalComments: Math.floor(8920 * multiplier),
        averageReadTime: 4.2 + (Math.random() - 0.5) * 0.5 // 小幅波动
      });
      setIsLoading(false);
    }, 1000);
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    setIsLoading(true);
    // In a real app, this would fetch new data
    setTimeout(() => setIsLoading(false), 500);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Content Analytics</h1>
        <div className="flex items-center space-x-2">
          <CompactDatePicker
            value={currentDateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            className="w-56"
          />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <FileText className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Total Articles</h2>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalArticles.toLocaleString()}</p>
              <p className="text-xs text-green-600">+8.2% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <Eye className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Total Views</h2>
              <p className="text-2xl font-semibold text-gray-900">342,580</p>
              <p className="text-xs text-green-600">+12.4% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <MessageSquare className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Comments</h2>
              <p className="text-2xl font-semibold text-gray-900">24,860</p>
              <p className="text-xs text-green-600">+15.7% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <Heart className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Likes</h2>
              <p className="text-2xl font-semibold text-gray-900">78,420</p>
              <p className="text-xs text-green-600">+9.8% from last period</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Content Publishing Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">Content Publishing</h2>
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              {currentDateRange ?
                `${currentDateRange.startDate.toLocaleDateString('en-US')} - ${currentDateRange.endDate.toLocaleDateString('en-US')}` :
                'Select date range'
              }
            </div>
          </div>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={contentPublishData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString('en-US')}
                    formatter={(value, name) => [
                      value,
                      name === 'articles' ? 'Articles Published' :
                      name === 'views' ? 'Total Views' : 'Comments'
                    ]}
                  />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="articles"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="Articles"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="views"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Views"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="comments"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Comments"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>

        {/* Category Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Category Performance</h2>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={categoryPerformanceData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="category" type="category" width={80} />
                  <Tooltip
                    formatter={(value, name) => [
                      value,
                      name === 'articles' ? 'Articles' :
                      name === 'views' ? 'Views' : 'Avg Engagement'
                    ]}
                  />
                  <Bar
                    dataKey="articles"
                    fill="#3b82f6"
                    name="Articles"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>

      {/* Top Content */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Top Performing Content</h2>

        {isLoading ? (
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Views
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Comments
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Likes
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Published
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {topContentData.map((content, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{content.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Eye className="h-4 w-4 mr-1" />
                        {content.views.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        {content.comments}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Heart className="h-4 w-4 mr-1" />
                        {content.likes}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {content.publishedAt}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentAnalyticsPage;
