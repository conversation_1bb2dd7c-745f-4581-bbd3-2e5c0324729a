'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Users,
  FileText,
  MessageSquare,
  BarChart3,
  Settings,
  ChevronDown,
  ChevronRight,
  DollarSign,
  AlertTriangle,
  Video,
  Music,
  CreditCard,
  Eye,
  UserCheck
} from 'lucide-react';

interface MenuItem {
  title: string;
  href?: string;
  icon: React.ReactNode;
  children?: MenuItem[];
}

const AdminSidebar: React.FC = () => {
  const pathname = usePathname();
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});

  // Default expand menus containing current path
  useEffect(() => {
    const initOpenMenus: Record<string, boolean> = {};

    if (pathname?.startsWith('/admin/users')) {
      initOpenMenus['User Management'] = true;
    }

    if (pathname?.startsWith('/admin/content')) {
      initOpenMenus['Content Management'] = true;
    }

    if (pathname?.startsWith('/admin/monetization') ||
        pathname?.startsWith('/admin/advertising') ||
        pathname?.startsWith('/admin/withdrawals')) {
      initOpenMenus['Revenue Management'] = true;
    }

    if (pathname?.startsWith('/admin/accounts')) {
      initOpenMenus['Account Management'] = true;
    }

    if (pathname?.startsWith('/admin/analytics')) {
      initOpenMenus['Data Analytics'] = true;
    }

    if (pathname?.startsWith('/admin/settings')) {
      initOpenMenus['System Settings'] = true;
    }

    setOpenMenus(initOpenMenus);
  }, [pathname]);

  const toggleMenu = (menuKey: string) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  const menuItems: MenuItem[] = [
    {
      title: 'Dashboard',
      href: '/admin/dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />
    },
    {
      title: 'User Management',
      icon: <Users className="w-5 h-5" />,
      children: [
        { title: 'User List', href: '/admin/users', icon: <div className="w-5 h-5" /> },
        { title: 'Role Management', href: '/admin/users/roles', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Account Management',
      icon: <UserCheck className="w-5 h-5" />,
      children: [
        { title: 'Account Overview', href: '/admin/accounts', icon: <UserCheck className="w-5 h-5" /> },
        { title: 'Verification', href: '/admin/accounts/verification', icon: <div className="w-5 h-5" /> },
        { title: 'Security', href: '/admin/accounts/security', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Content Management',
      icon: <FileText className="w-5 h-5" />,
      children: [
        { title: 'All Content', href: '/admin/content', icon: <div className="w-5 h-5" /> },
        { title: 'Articles', href: '/admin/content/articles', icon: <div className="w-5 h-5" /> },
        { title: 'Videos', href: '/admin/content/videos', icon: <Video className="w-5 h-5" /> },
        { title: 'Audio', href: '/admin/content/audio', icon: <Music className="w-5 h-5" /> },
        { title: 'Comments', href: '/admin/content/comments', icon: <MessageSquare className="w-5 h-5" /> },
        { title: 'Categories', href: '/admin/content/categories', icon: <div className="w-5 h-5" /> },
        { title: 'Tags', href: '/admin/content/tags', icon: <div className="w-5 h-5" /> },
        { title: 'Review Queue', href: '/admin/content/reviews', icon: <div className="w-5 h-5" /> },
        { title: 'Reports', href: '/admin/content/reports', icon: <AlertTriangle className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Revenue Management',
      icon: <DollarSign className="w-5 h-5" />,
      children: [
        { title: 'Revenue Overview', href: '/admin/monetization', icon: <DollarSign className="w-5 h-5" /> },
        { title: 'Advertising Management', href: '/admin/advertising', icon: <Eye className="w-5 h-5" /> },
        { title: 'Withdrawal Management', href: '/admin/withdrawals', icon: <CreditCard className="w-5 h-5" /> }
      ]
    },
    {
      title: 'Data Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      children: [
        { title: 'Overview', href: '/admin/analytics', icon: <div className="w-5 h-5" /> },
        { title: 'User Analytics', href: '/admin/analytics/users', icon: <div className="w-5 h-5" /> },
        { title: 'Content Analytics', href: '/admin/analytics/content', icon: <div className="w-5 h-5" /> },
        { title: 'System Analytics', href: '/admin/analytics/system', icon: <div className="w-5 h-5" /> }
      ]
    },
    {
      title: 'System Settings',
      icon: <Settings className="w-5 h-5" />,
      children: [
        { title: 'General', href: '/admin/settings', icon: <div className="w-5 h-5" /> },
        { title: 'General Settings', href: '/admin/settings/general', icon: <div className="w-5 h-5" /> },
        { title: 'Security Settings', href: '/admin/settings/security', icon: <div className="w-5 h-5" /> },
        { title: 'Email Settings', href: '/admin/settings/email', icon: <div className="w-5 h-5" /> }
      ]
    }
  ];

  const isActive = (href?: string) => {
    if (!href) return false;
    return pathname === href;
  };

  const isMenuActive = (item: MenuItem) => {
    if (isActive(item.href)) return true;
    
    if (item.children) {
      return item.children.some(child => isActive(child.href));
    }
    
    return false;
  };

  return (
    <aside className="w-64 bg-gray-800 h-full fixed left-0 top-16 overflow-y-auto">
      <nav className="p-4">
        <ul className="space-y-1">
          {menuItems.map((item, index) => (
            <li key={index}>
              {item.href ? (
                <Link
                  href={item.href}
                  className={`flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg ${
                    isActive(item.href) ? 'bg-blue-700 text-white' : ''
                  }`}
                >
                  <span className="mr-3">{item.icon}</span>
                  <span className="font-medium">{item.title}</span>
                </Link>
              ) : (
                <>
                  <button
                    onClick={() => toggleMenu(item.title)}
                    className={`w-full flex items-center justify-between px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg ${
                      isMenuActive(item) ? 'bg-gray-700 text-white' : ''
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="mr-3">{item.icon}</span>
                      <span className="font-medium">{item.title}</span>
                    </div>
                    {openMenus[item.title] ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                  
                  {openMenus[item.title] && item.children && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {item.children.map((child, childIndex) => (
                        <li key={childIndex}>
                          <Link
                            href={child.href || '#'}
                            className={`flex items-center px-4 py-2 text-sm text-gray-400 hover:bg-gray-700 hover:text-white rounded-lg ${
                              isActive(child.href) ? 'bg-gray-700 text-white' : ''
                            }`}
                          >
                            <span className="mr-3">{child.icon}</span>
                            <span>{child.title}</span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default AdminSidebar;